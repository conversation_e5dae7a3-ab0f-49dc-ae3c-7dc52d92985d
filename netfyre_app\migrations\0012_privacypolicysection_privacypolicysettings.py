# Generated by Django 5.2.1 on 2025-06-21 04:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0011_sitesettings_storytellers_description_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PrivacyPolicySection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Section title', max_length=200)),
                ('content', models.TextField(help_text='Section content (supports HTML)')),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Privacy Policy Section',
                'verbose_name_plural': 'Privacy Policy Sections',
                'ordering': ['order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='PrivacyPolicySettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('page_title', models.CharField(default='Privacy Policy', help_text='Main page title', max_length=200)),
                ('subtitle', models.TextField(default='Your privacy is important to us. This privacy policy explains how we collect, use, and protect your information when you use our services.', help_text='Page subtitle/description')),
                ('contact_title', models.CharField(default='Contact Us', help_text='Contact section title', max_length=200)),
                ('contact_description', models.TextField(default='If you have any questions about this Privacy Policy, please contact us:', help_text='Contact section description')),
                ('contact_email', models.EmailField(default='<EMAIL>', help_text='Privacy contact email', max_length=254)),
                ('contact_phone', models.CharField(default='+****************', help_text='Privacy contact phone', max_length=20)),
                ('contact_address', models.TextField(default='123 Business Street, City, State 12345', help_text='Privacy contact address')),
                ('last_updated', models.DateTimeField(auto_now=True, help_text='Last updated timestamp')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Privacy Policy Settings',
                'verbose_name_plural': 'Privacy Policy Settings',
            },
        ),
    ]
