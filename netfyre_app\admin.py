from django.contrib import admin
from django.utils.html import format_html
from .models import Service, Testimonial, Project, CompanyLogo, FAQ, SiteSettings, Counter, GalleryImage, AboutSection, CaseStudy, InfiniteScrollImage, TrustedCompany, TeamMember, TeamMemberPortfolioImage, TeamMemberPortfolioVideo, TeamMemberProject, ContactSubmission, PrivacyPolicySection, PrivacyPolicySettings, CookieConsent, CookieActivity, TermsAndConditionsSection, TermsAndConditionsSettings


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'order', 'is_active', 'created_at']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['title', 'subtitle', 'category']
    list_editable = ['order', 'is_active']
    ordering = ['order', 'title']


@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = ['client_name', 'client_company', 'rating', 'is_featured', 'is_active', 'created_at']
    list_filter = ['rating', 'is_featured', 'is_active', 'created_at']
    search_fields = ['client_name', 'client_company', 'testimonial_text']
    list_editable = ['is_featured', 'is_active']
    ordering = ['-is_featured', '-created_at']


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['title', 'client', 'completion_date', 'is_featured', 'is_active', 'order']
    list_filter = ['is_featured', 'is_active', 'completion_date']
    search_fields = ['title', 'description', 'client', 'technologies']
    list_editable = ['is_featured', 'is_active', 'order']
    ordering = ['-is_featured', 'order', '-completion_date']
    date_hierarchy = 'completion_date'


@admin.register(CompanyLogo)
class CompanyLogoAdmin(admin.ModelAdmin):
    list_display = ['company_name', 'order', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['company_name']
    list_editable = ['order', 'is_active']
    ordering = ['order', 'company_name']


@admin.register(FAQ)
class FAQAdmin(admin.ModelAdmin):
    list_display = ['question', 'order', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['question', 'answer']
    list_editable = ['order', 'is_active']
    ordering = ['order', 'question']


@admin.register(SiteSettings)
class SiteSettingsAdmin(admin.ModelAdmin):
    list_display = ['site_title', 'contact_email', 'updated_at']
    fieldsets = (
        ('Basic Information', {
            'fields': ('site_title', 'site_tagline', 'contact_email', 'phone_number', 'address')
        }),
        ('Hero Section', {
            'fields': ('hero_title', 'hero_subtitle')
        }),
        ('Team Section', {
            'fields': ('team_title', 'team_subtitle', 'storytellers_title', 'storytellers_description')
        }),
        ('Media', {
            'fields': ('logo', 'favicon')
        }),
        ('Social Media', {
            'fields': ('facebook_url', 'twitter_url', 'linkedin_url', 'instagram_url')
        }),
        ('Analytics', {
            'fields': ('google_analytics_id', 'facebook_pixel_id')
        }),
    )

    def has_add_permission(self, request):
        # Only allow one instance
        return not SiteSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of the settings
        return False


@admin.register(Counter)
class CounterAdmin(admin.ModelAdmin):
    list_display = ['label', 'value', 'prefix', 'suffix', 'order', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['label']
    list_editable = ['order', 'is_active']
    ordering = ['order', 'label']


@admin.register(GalleryImage)
class GalleryImageAdmin(admin.ModelAdmin):
    list_display = ['title', 'column', 'order', 'is_active', 'created_at']
    list_filter = ['column', 'is_active', 'created_at']
    search_fields = ['title', 'alt_text']
    list_editable = ['column', 'order', 'is_active']
    ordering = ['column', 'order', 'title']

    fieldsets = (
        ('Image Information', {
            'fields': ('title', 'image', 'alt_text')
        }),
        ('Display Settings', {
            'fields': ('column', 'order', 'is_active')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related()


@admin.register(AboutSection)
class AboutSectionAdmin(admin.ModelAdmin):
    list_display = ['title', 'is_active', 'updated_at']

    fieldsets = (
        ('Section Content', {
            'fields': ('title', 'subtitle', 'description', 'is_active')
        }),
        ('Video Settings', {
            'fields': ('video_file', 'video_url', 'video_poster'),
            'description': 'Upload a video file OR provide a video URL (not both). Video file takes priority.'
        }),
        ('Video Controls', {
            'fields': ('autoplay', 'muted', 'loop', 'controls'),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        # Only allow one instance
        return not AboutSection.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of the about section
        return False


@admin.register(CaseStudy)
class CaseStudyAdmin(admin.ModelAdmin):
    list_display = ['title', 'client_name', 'project_category', 'is_featured', 'is_active', 'order', 'updated_at']
    list_filter = ['is_featured', 'is_active', 'project_category', 'created_at', 'updated_at']
    search_fields = ['title', 'client_name', 'description', 'excerpt']
    list_editable = ['is_featured', 'is_active', 'order']
    prepopulated_fields = {'slug': ('title',)}
    ordering = ['-is_featured', 'order', '-created_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug', 'client_name', 'project_category', 'excerpt')
        }),
        ('Top Section Images', {
            'fields': ('image_1', 'image_1_alt', 'image_2', 'image_2_alt'),
            'description': 'Upload two curved square images for the top section'
        }),
        ('Content', {
            'fields': ('description',)
        }),
        ('Hover Image', {
            'fields': ('hover_image', 'hover_image_alt'),
            'description': 'Center image with hover animation effect'
        }),
        ('Video Settings', {
            'fields': ('video_file', 'video_url', 'video_poster'),
            'description': 'Upload a video file OR provide a video URL (not both). Video file takes priority. NOTE: Video poster image also serves as the overlay logo for the second curved square image.'
        }),
        ('Video Controls', {
            'fields': ('video_autoplay', 'video_muted', 'video_loop', 'video_controls'),
            'classes': ('collapse',)
        }),
        ('Display Settings', {
            'fields': ('is_featured', 'is_active', 'order')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related()


@admin.register(InfiniteScrollImage)
class InfiniteScrollImageAdmin(admin.ModelAdmin):
    list_display = ['title', 'order', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['title', 'alt_text']
    list_editable = ['order', 'is_active']
    ordering = ['order', 'title']

    fieldsets = (
        ('Image Information', {
            'fields': ('title', 'image', 'alt_text')
        }),
        ('Display Settings', {
            'fields': ('order', 'is_active')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related()


@admin.register(TrustedCompany)
class TrustedCompanyAdmin(admin.ModelAdmin):
    list_display = ['company_name', 'order', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['company_name', 'website_url']
    list_editable = ['order', 'is_active']
    ordering = ['order', 'company_name']

    fieldsets = (
        ('Company Information', {
            'fields': ('company_name', 'logo_image', 'website_url')
        }),
        ('Display Settings', {
            'fields': ('order', 'is_active')
        }),
    )


class TeamMemberPortfolioImageInline(admin.TabularInline):
    model = TeamMemberPortfolioImage
    extra = 1
    fields = ['title', 'image', 'category', 'order', 'is_featured', 'is_active']
    ordering = ['order']


class TeamMemberPortfolioVideoInline(admin.TabularInline):
    model = TeamMemberPortfolioVideo
    extra = 1
    fields = ['title', 'video_file', 'video_url', 'thumbnail', 'category', 'order', 'is_featured', 'is_active']
    ordering = ['order']


class TeamMemberProjectInline(admin.TabularInline):
    model = TeamMemberProject
    extra = 1
    fields = ['title', 'client', 'role', 'featured_image', 'completion_date', 'order', 'is_featured', 'is_active']
    ordering = ['order']


@admin.register(TeamMember)
class TeamMemberAdmin(admin.ModelAdmin):
    list_display = ['name', 'get_display_role', 'years_experience', 'is_featured', 'is_active', 'order', 'updated_at']
    list_filter = ['role', 'is_featured', 'is_active', 'years_experience', 'created_at']
    search_fields = ['name', 'role', 'custom_role', 'bio', 'specialties']
    list_editable = ['is_featured', 'is_active', 'order']
    ordering = ['order', 'name']
    inlines = [TeamMemberPortfolioImageInline, TeamMemberPortfolioVideoInline, TeamMemberProjectInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'role', 'custom_role', 'short_bio', 'bio')
        }),
        ('Images', {
            'fields': ('profile_image', 'cover_image')
        }),
        ('Professional Details', {
            'fields': ('years_experience', 'specialties', 'education', 'achievements')
        }),
        ('Contact Information', {
            'fields': ('email', 'phone', 'personal_website'),
            'classes': ('collapse',)
        }),
        ('Social Media', {
            'fields': ('linkedin_url', 'twitter_url', 'instagram_url', 'behance_url', 'dribbble_url', 'github_url'),
            'classes': ('collapse',)
        }),
        ('Display Settings', {
            'fields': ('order', 'is_featured', 'is_active', 'show_contact')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related()


@admin.register(TeamMemberPortfolioImage)
class TeamMemberPortfolioImageAdmin(admin.ModelAdmin):
    list_display = ['title', 'team_member', 'category', 'is_featured', 'is_active', 'order', 'created_at']
    list_filter = ['team_member', 'category', 'is_featured', 'is_active', 'created_at']
    search_fields = ['title', 'description', 'category', 'team_member__name']
    list_editable = ['is_featured', 'is_active', 'order']
    ordering = ['team_member', 'order', '-created_at']

    fieldsets = (
        ('Image Information', {
            'fields': ('team_member', 'title', 'image', 'description')
        }),
        ('Project Details', {
            'fields': ('category', 'project_url')
        }),
        ('Display Settings', {
            'fields': ('order', 'is_featured', 'is_active')
        }),
    )


@admin.register(TeamMemberPortfolioVideo)
class TeamMemberPortfolioVideoAdmin(admin.ModelAdmin):
    list_display = ['title', 'team_member', 'category', 'duration', 'is_featured', 'is_active', 'order', 'created_at']
    list_filter = ['team_member', 'category', 'is_featured', 'is_active', 'created_at']
    search_fields = ['title', 'description', 'category', 'team_member__name']
    list_editable = ['is_featured', 'is_active', 'order']
    ordering = ['team_member', 'order', '-created_at']

    fieldsets = (
        ('Video Information', {
            'fields': ('team_member', 'title', 'description', 'duration')
        }),
        ('Video Source', {
            'fields': ('video_file', 'video_url', 'thumbnail'),
            'description': 'Upload a video file OR provide a video URL (not both). Video file takes priority.'
        }),
        ('Project Details', {
            'fields': ('category',)
        }),
        ('Display Settings', {
            'fields': ('order', 'is_featured', 'is_active')
        }),
    )


@admin.register(TeamMemberProject)
class TeamMemberProjectAdmin(admin.ModelAdmin):
    list_display = ['title', 'team_member', 'client', 'role', 'completion_date', 'is_featured', 'is_active', 'order']
    list_filter = ['team_member', 'is_featured', 'is_active', 'completion_date', 'created_at']
    search_fields = ['title', 'description', 'client', 'role', 'technologies', 'team_member__name']
    list_editable = ['is_featured', 'is_active', 'order']
    ordering = ['team_member', 'order', '-completion_date']
    date_hierarchy = 'completion_date'

    fieldsets = (
        ('Project Information', {
            'fields': ('team_member', 'title', 'description', 'featured_image')
        }),
        ('Project Details', {
            'fields': ('client', 'role', 'technologies', 'completion_date')
        }),
        ('Links', {
            'fields': ('project_url', 'github_url')
        }),
        ('Display Settings', {
            'fields': ('order', 'is_featured', 'is_active')
        }),
    )


@admin.register(ContactSubmission)
class ContactSubmissionAdmin(admin.ModelAdmin):
    list_display = ['fullname', 'email', 'phone', 'business_type', 'revenue', 'source', 'submitted_at', 'is_read']
    list_filter = ['business_type', 'revenue', 'source', 'is_read', 'submitted_at']
    search_fields = ['fullname', 'email', 'phone', 'website', 'business_type']
    list_editable = ['is_read']
    readonly_fields = ['submitted_at']
    ordering = ['-submitted_at']

    fieldsets = (
        ('Contact Information', {
            'fields': ('fullname', 'email', 'phone', 'website')
        }),
        ('Business Information', {
            'fields': ('business_type', 'revenue', 'source')
        }),
        ('Admin', {
            'fields': ('submitted_at', 'is_read')
        }),
    )

    def has_add_permission(self, request):
        # Don't allow adding contact submissions through admin
        return False


@admin.register(PrivacyPolicySection)
class PrivacyPolicySectionAdmin(admin.ModelAdmin):
    list_display = ['title', 'order', 'is_active', 'updated_at']
    list_filter = ['is_active', 'created_at', 'updated_at']
    search_fields = ['title', 'content']
    list_editable = ['order', 'is_active']
    ordering = ['order', 'title']

    fieldsets = (
        ('Section Information', {
            'fields': ('title', 'content')
        }),
        ('Display Settings', {
            'fields': ('order', 'is_active')
        }),
    )


@admin.register(PrivacyPolicySettings)
class PrivacyPolicySettingsAdmin(admin.ModelAdmin):
    list_display = ['page_title', 'contact_email', 'last_updated']
    readonly_fields = ['last_updated']

    fieldsets = (
        ('Page Content', {
            'fields': ('page_title', 'subtitle')
        }),
        ('Contact Information', {
            'fields': ('contact_title', 'contact_description', 'contact_email', 'contact_phone', 'contact_address')
        }),
        ('Settings', {
            'fields': ('is_active', 'last_updated')
        }),
    )

    def has_add_permission(self, request):
        # Only allow one instance
        return not PrivacyPolicySettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of the settings
        return False


class CookieActivityInline(admin.TabularInline):
    model = CookieActivity
    extra = 0
    readonly_fields = ['action', 'timestamp', 'page_url', 'settings_snapshot']
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(CookieConsent)
class CookieConsentAdmin(admin.ModelAdmin):
    list_display = [
        'ip_address',
        'consent_status_display',
        'enabled_categories_display',
        'created_at',
        'updated_at'
    ]
    list_filter = [
        'consent_given',
        'consent_declined',
        'analytics_cookies',
        'marketing_cookies',
        'preferences_cookies',
        'created_at',
        'updated_at'
    ]
    search_fields = ['ip_address', 'session_key', 'page_url']
    readonly_fields = [
        'session_key',
        'ip_address',
        'user_agent',
        'created_at',
        'updated_at',
        'consent_status',
        'enabled_categories'
    ]

    fieldsets = (
        ('User Information', {
            'fields': ('session_key', 'ip_address', 'user_agent')
        }),
        ('Consent Status', {
            'fields': ('consent_given', 'consent_declined', 'consent_status')
        }),
        ('Cookie Categories', {
            'fields': (
                'necessary_cookies',
                'analytics_cookies',
                'marketing_cookies',
                'preferences_cookies',
                'enabled_categories'
            )
        }),
        ('Context Information', {
            'fields': ('page_url', 'referrer')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    inlines = [CookieActivityInline]

    def consent_status_display(self, obj):
        status = obj.consent_status
        colors = {
            'Accepted': 'green',
            'Declined': 'red',
            'Pending': 'orange'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(status, 'black'),
            status
        )
    consent_status_display.short_description = 'Status'

    def enabled_categories_display(self, obj):
        categories = obj.enabled_categories
        if not categories:
            return format_html('<span style="color: gray;">None</span>')
        return format_html('<span style="color: green;">{}</span>', categories)
    enabled_categories_display.short_description = 'Enabled Categories'

    def has_add_permission(self, request):
        # Don't allow manual creation of consent records
        return False

    def has_delete_permission(self, request, obj=None):
        # Allow deletion for data cleanup
        return True


@admin.register(CookieActivity)
class CookieActivityAdmin(admin.ModelAdmin):
    list_display = [
        'consent_ip',
        'action_display',
        'timestamp',
        'page_url_short'
    ]
    list_filter = [
        'action',
        'timestamp',
    ]
    search_fields = ['consent__ip_address', 'page_url']
    readonly_fields = [
        'consent',
        'action',
        'timestamp',
        'page_url',
        'user_agent',
        'settings_snapshot'
    ]

    fieldsets = (
        ('Activity Information', {
            'fields': ('consent', 'action', 'timestamp')
        }),
        ('Context', {
            'fields': ('page_url', 'user_agent')
        }),
        ('Settings Data', {
            'fields': ('settings_snapshot',),
            'classes': ('collapse',)
        }),
    )

    def consent_ip(self, obj):
        return obj.consent.ip_address
    consent_ip.short_description = 'IP Address'

    def action_display(self, obj):
        action_colors = {
            'banner_shown': 'blue',
            'accepted_all': 'green',
            'declined_all': 'red',
            'settings_opened': 'orange',
            'settings_saved': 'purple',
            'consent_updated': 'teal'
        }
        color = action_colors.get(obj.action, 'black')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_action_display()
        )
    action_display.short_description = 'Action'

    def page_url_short(self, obj):
        if obj.page_url:
            if len(obj.page_url) > 50:
                return obj.page_url[:47] + '...'
            return obj.page_url
        return '-'
    page_url_short.short_description = 'Page URL'

    def has_add_permission(self, request):
        # Don't allow manual creation of activity records
        return False

    def has_change_permission(self, request, obj=None):
        # Don't allow editing of activity records
        return False


@admin.register(TermsAndConditionsSection)
class TermsAndConditionsSectionAdmin(admin.ModelAdmin):
    list_display = ['title', 'order', 'is_active', 'updated_at']
    list_filter = ['is_active', 'created_at', 'updated_at']
    search_fields = ['title', 'content']
    list_editable = ['order', 'is_active']
    ordering = ['order', 'title']

    fieldsets = (
        ('Section Information', {
            'fields': ('title', 'content')
        }),
        ('Display Settings', {
            'fields': ('order', 'is_active')
        }),
    )


@admin.register(TermsAndConditionsSettings)
class TermsAndConditionsSettingsAdmin(admin.ModelAdmin):
    list_display = ['page_title', 'effective_date', 'contact_email', 'last_updated']
    readonly_fields = ['last_updated']

    fieldsets = (
        ('Page Content', {
            'fields': ('page_title', 'subtitle', 'effective_date')
        }),
        ('Contact Information', {
            'fields': ('contact_title', 'contact_description', 'contact_email', 'contact_phone', 'contact_address')
        }),
        ('Settings', {
            'fields': ('is_active', 'last_updated')
        }),
    )

    def has_add_permission(self, request):
        # Only allow one instance
        return not TermsAndConditionsSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of the settings
        return False
