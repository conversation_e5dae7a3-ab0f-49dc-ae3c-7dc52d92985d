# Generated by Django 5.2.1 on 2025-06-20 01:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0006_contactsubmission'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='contactsubmission',
            name='message',
        ),
        migrations.AddField(
            model_name='contactsubmission',
            name='business_type',
            field=models.CharField(choices=[('ecommerce', 'E-commerce'), ('saas', 'SaaS/Software'), ('healthcare', 'Healthcare'), ('finance', 'Finance/Banking'), ('education', 'Education'), ('real-estate', 'Real Estate'), ('restaurant', 'Restaurant/Food'), ('fitness', 'Fitness/Wellness'), ('consulting', 'Consulting'), ('manufacturing', 'Manufacturing'), ('nonprofit', 'Non-profit'), ('other', 'Other')], default='other', help_text='Type of business', max_length=30),
        ),
    ]
