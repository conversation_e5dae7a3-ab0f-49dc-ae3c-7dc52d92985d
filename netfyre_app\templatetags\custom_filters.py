from django import template
import re

register = template.Library()

@register.filter
def youtube_embed(url):
    """
    Convert YouTube URL to embeddable format
    """
    if not url:
        return ""
    
    # Handle different YouTube URL formats
    youtube_regex = r'(?:https?://)?(?:www\.)?(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/)([a-zA-Z0-9_-]+)'
    match = re.search(youtube_regex, url)
    
    if match:
        video_id = match.group(1)
        # Remove any additional parameters from video_id
        video_id = video_id.split('&')[0].split('?')[0]
        return f"https://www.youtube.com/embed/{video_id}?autoplay=0&mute=1&controls=1"
    
    return url  # Return original URL if not YouTube
