#!/usr/bin/env python
"""
Script to manually add overlay_logo fields to the CaseStudy model
"""
import os
import django
import sqlite3

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'netfyre_project.settings')
django.setup()

def add_overlay_fields():
    """Add overlay_logo and overlay_logo_alt fields to the database"""
    try:
        # Connect to SQLite database
        conn = sqlite3.connect('db.sqlite3')
        cursor = conn.cursor()
        
        # Check if fields already exist
        cursor.execute("PRAGMA table_info(netfyre_app_casestudy)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'overlay_logo' not in columns:
            print("Adding overlay_logo field...")
            cursor.execute("""
                ALTER TABLE netfyre_app_casestudy 
                ADD COLUMN overlay_logo VARCHAR(100) NULL
            """)
            
        if 'overlay_logo_alt' not in columns:
            print("Adding overlay_logo_alt field...")
            cursor.execute("""
                ALTER TABLE netfyre_app_casestudy 
                ADD COLUMN overlay_logo_alt VARCHAR(200) DEFAULT 'Overlay Logo'
            """)
            
        # Commit changes
        conn.commit()
        print("✅ Successfully added overlay logo fields!")
        
        # Verify fields were added
        cursor.execute("PRAGMA table_info(netfyre_app_casestudy)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'overlay_logo' in columns and 'overlay_logo_alt' in columns:
            print("✅ Fields verified in database!")
        else:
            print("❌ Fields not found after adding!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    add_overlay_fields()
