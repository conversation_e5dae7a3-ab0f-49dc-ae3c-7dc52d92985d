from django.core.management.base import BaseCommand
from netfyre_app.models import PrivacyPolicySection, PrivacyPolicySettings


class Command(BaseCommand):
    help = 'Populate privacy policy with default content'

    def handle(self, *args, **options):
        # Create or update privacy policy settings
        settings, created = PrivacyPolicySettings.objects.get_or_create(
            defaults={
                'page_title': 'Privacy Policy',
                'subtitle': 'Your privacy is important to us. This privacy policy explains how we collect, use, and protect your information when you use our services.',
                'contact_title': 'Contact Us',
                'contact_description': 'If you have any questions about this Privacy Policy, please contact us:',
                'contact_email': '<EMAIL>',
                'contact_phone': '+****************',
                'contact_address': '123 Business Street, City, State 12345',
            }
        )

        if created:
            self.stdout.write(self.style.SUCCESS('Created privacy policy settings'))
        else:
            self.stdout.write(self.style.WARNING('Privacy policy settings already exist'))

        # Default privacy policy sections
        sections_data = [
            {
                'title': 'Information We Collect',
                'content': '''<p>We collect information you provide directly to us, such as when you create an account, contact us, or use our services. This may include:</p>
<ul>
<li>Name and contact information (email address, phone number)</li>
<li>Business information (company name, industry, website)</li>
<li>Communication preferences and marketing preferences</li>
<li>Any other information you choose to provide</li>
</ul>''',
                'order': 1
            },
            {
                'title': 'How We Use Your Information',
                'content': '''<p>We use the information we collect to:</p>
<ul>
<li>Provide, maintain, and improve our services</li>
<li>Process transactions and send related information</li>
<li>Send you technical notices, updates, security alerts, and support messages</li>
<li>Respond to your comments, questions, and customer service requests</li>
<li>Communicate with you about products, services, offers, and events</li>
<li>Monitor and analyze trends, usage, and activities in connection with our services</li>
</ul>''',
                'order': 2
            },
            {
                'title': 'Information Sharing and Disclosure',
                'content': '''<p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this privacy policy:</p>
<ul>
<li>With your consent or at your direction</li>
<li>With service providers who assist us in operating our business</li>
<li>To comply with legal obligations or protect our rights</li>
<li>In connection with a business transaction (merger, acquisition, etc.)</li>
</ul>''',
                'order': 3
            },
            {
                'title': 'Data Security',
                'content': '''<p>We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet or electronic storage is 100% secure.</p>''',
                'order': 4
            },
            {
                'title': 'Your Rights and Choices',
                'content': '''<p>You have the right to:</p>
<ul>
<li>Access, update, or delete your personal information</li>
<li>Opt out of marketing communications</li>
<li>Request a copy of your personal information</li>
<li>Object to processing of your personal information</li>
</ul>''',
                'order': 5
            },
            {
                'title': 'Cookies and Tracking Technologies',
                'content': '''<p>We use cookies and similar tracking technologies to collect and track information about your use of our services. You can control cookies through your browser settings.</p>''',
                'order': 6
            },
            {
                'title': 'Changes to This Privacy Policy',
                'content': '''<p>We may update this privacy policy from time to time. We will notify you of any changes by posting the new privacy policy on this page and updating the "Last Updated" date.</p>''',
                'order': 7
            }
        ]

        created_count = 0
        for section_data in sections_data:
            section, created = PrivacyPolicySection.objects.get_or_create(
                title=section_data['title'],
                defaults={
                    'content': section_data['content'],
                    'order': section_data['order'],
                    'is_active': True
                }
            )
            if created:
                created_count += 1

        if created_count > 0:
            self.stdout.write(
                self.style.SUCCESS(f'Created {created_count} privacy policy sections')
            )
        else:
            self.stdout.write(
                self.style.WARNING('All privacy policy sections already exist')
            )

        self.stdout.write(
            self.style.SUCCESS('Privacy policy population completed!')
        )
