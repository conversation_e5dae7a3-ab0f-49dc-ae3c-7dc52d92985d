from django.core.management.base import BaseCommand
from netfyre_app.models import GalleryImage


class Command(BaseCommand):
    help = 'Populate the database with sample gallery images'

    def handle(self, *args, **options):
        self.stdout.write('Populating database with sample gallery images...')

        # Sample gallery images data
        gallery_images_data = [
            # Column 1 images
            {
                'title': 'Creative Design Showcase',
                'column': 1,
                'order': 1,
                'alt_text': 'Creative design showcase image'
            },
            {
                'title': 'Brand Identity Project',
                'column': 1,
                'order': 2,
                'alt_text': 'Brand identity design project'
            },
            {
                'title': 'Digital Marketing Campaign',
                'column': 1,
                'order': 3,
                'alt_text': 'Digital marketing campaign visual'
            },
            {
                'title': 'Web Development Portfolio',
                'column': 1,
                'order': 4,
                'alt_text': 'Web development portfolio piece'
            },
            
            # Column 2 images
            {
                'title': 'UI/UX Design Excellence',
                'column': 2,
                'order': 1,
                'alt_text': 'UI/UX design excellence showcase'
            },
            {
                'title': 'Mobile App Interface',
                'column': 2,
                'order': 2,
                'alt_text': 'Mobile app interface design'
            },
            {
                'title': 'E-commerce Solution',
                'column': 2,
                'order': 3,
                'alt_text': 'E-commerce solution showcase'
            },
            {
                'title': 'Social Media Graphics',
                'column': 2,
                'order': 4,
                'alt_text': 'Social media graphics design'
            },
        ]

        for image_data in gallery_images_data:
            gallery_image, created = GalleryImage.objects.get_or_create(
                title=image_data['title'],
                column=image_data['column'],
                defaults=image_data
            )
            if created:
                self.stdout.write(f'✓ Gallery image created: {gallery_image.title}')
            else:
                self.stdout.write(f'- Gallery image already exists: {gallery_image.title}')

        self.stdout.write(
            self.style.SUCCESS('Successfully populated database with sample gallery images!')
        )
        self.stdout.write(
            self.style.WARNING('Note: You still need to upload actual images through the Django admin panel.')
        )
