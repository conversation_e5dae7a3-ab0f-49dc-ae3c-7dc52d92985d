from django.core.management.base import BaseCommand
from netfyre_app.models import TermsAndConditionsSection, TermsAndConditionsSettings
from datetime import date


class Command(BaseCommand):
    help = 'Populate terms and conditions with default content'

    def handle(self, *args, **options):
        # Create or update terms and conditions settings
        settings, created = TermsAndConditionsSettings.objects.get_or_create(
            defaults={
                'page_title': 'Terms and Conditions',
                'subtitle': 'Please read these terms and conditions carefully before using our services. By accessing or using our website, you agree to be bound by these terms.',
                'effective_date': date.today(),
                'contact_title': 'Contact Us',
                'contact_description': 'If you have any questions about these Terms and Conditions, please contact us:',
                'contact_email': '<EMAIL>',
                'contact_phone': '+****************',
                'contact_address': '123 Business Street, City, State 12345',
            }
        )

        if created:
            self.stdout.write(self.style.SUCCESS('Created terms and conditions settings'))
        else:
            self.stdout.write(self.style.WARNING('Terms and conditions settings already exist'))

        # Default terms and conditions sections
        sections_data = [
            {
                'title': 'Acceptance of Terms',
                'content': '''<p>By accessing and using this website, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.</p>
<p>These terms and conditions constitute a legally binding agreement between you and NetFyre regarding your use of our website and services.</p>''',
                'order': 1
            },
            {
                'title': 'Use License',
                'content': '''<p>Permission is granted to temporarily download one copy of the materials on NetFyre's website for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:</p>
<ul>
<li>modify or copy the materials</li>
<li>use the materials for any commercial purpose or for any public display (commercial or non-commercial)</li>
<li>attempt to decompile or reverse engineer any software contained on the website</li>
<li>remove any copyright or other proprietary notations from the materials</li>
</ul>
<p>This license shall automatically terminate if you violate any of these restrictions and may be terminated by NetFyre at any time.</p>''',
                'order': 2
            },
            {
                'title': 'Disclaimer',
                'content': '''<p>The materials on NetFyre's website are provided on an 'as is' basis. NetFyre makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.</p>
<p>Further, NetFyre does not warrant or make any representations concerning the accuracy, likely results, or reliability of the use of the materials on its website or otherwise relating to such materials or on any sites linked to this site.</p>''',
                'order': 3
            },
            {
                'title': 'Limitations',
                'content': '''<p>In no event shall NetFyre or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on NetFyre's website, even if NetFyre or a NetFyre authorized representative has been notified orally or in writing of the possibility of such damage.</p>
<p>Because some jurisdictions do not allow limitations on implied warranties, or limitations of liability for consequential or incidental damages, these limitations may not apply to you.</p>''',
                'order': 4
            },
            {
                'title': 'Accuracy of Materials',
                'content': '''<p>The materials appearing on NetFyre's website could include technical, typographical, or photographic errors. NetFyre does not warrant that any of the materials on its website are accurate, complete, or current.</p>
<p>NetFyre may make changes to the materials contained on its website at any time without notice. However, NetFyre does not make any commitment to update the materials.</p>''',
                'order': 5
            },
            {
                'title': 'Links',
                'content': '''<p>NetFyre has not reviewed all of the sites linked to our website and is not responsible for the contents of any such linked site. The inclusion of any link does not imply endorsement by NetFyre of the site.</p>
<p>Use of any such linked website is at the user's own risk.</p>''',
                'order': 6
            },
            {
                'title': 'Modifications',
                'content': '''<p>NetFyre may revise these terms of service for its website at any time without notice. By using this website, you are agreeing to be bound by the then current version of these terms of service.</p>
<p>We will notify users of any material changes to these terms through our website or other appropriate means.</p>''',
                'order': 7
            },
            {
                'title': 'Governing Law',
                'content': '''<p>These terms and conditions are governed by and construed in accordance with the laws of [Your Jurisdiction] and you irrevocably submit to the exclusive jurisdiction of the courts in that state or location.</p>
<p>Any disputes arising from these terms shall be resolved through binding arbitration in accordance with the rules of the American Arbitration Association.</p>''',
                'order': 8
            },
            {
                'title': 'User Conduct',
                'content': '''<p>You agree to use our website only for lawful purposes and in a way that does not infringe the rights of, restrict or inhibit anyone else's use and enjoyment of the website.</p>
<p>Prohibited behavior includes:</p>
<ul>
<li>Harassing or causing distress or inconvenience to any other user</li>
<li>Transmitting obscene or offensive content</li>
<li>Disrupting the normal flow of dialogue within our website</li>
<li>Attempting to gain unauthorized access to our systems</li>
</ul>''',
                'order': 9
            },
            {
                'title': 'Intellectual Property',
                'content': '''<p>All content on this website, including but not limited to text, graphics, logos, images, audio clips, video clips, digital downloads, data compilations, and software, is the property of NetFyre or its content suppliers and is protected by copyright laws.</p>
<p>The compilation of all content on this site is the exclusive property of NetFyre and is protected by copyright laws. All software used on this site is the property of NetFyre or its software suppliers and is protected by copyright laws.</p>''',
                'order': 10
            }
        ]

        created_count = 0
        for section_data in sections_data:
            section, created = TermsAndConditionsSection.objects.get_or_create(
                title=section_data['title'],
                defaults={
                    'content': section_data['content'],
                    'order': section_data['order'],
                    'is_active': True
                }
            )
            if created:
                created_count += 1

        if created_count > 0:
            self.stdout.write(
                self.style.SUCCESS(f'Created {created_count} terms and conditions sections')
            )
        else:
            self.stdout.write(
                self.style.WARNING('All terms and conditions sections already exist')
            )

        self.stdout.write(
            self.style.SUCCESS('Terms and conditions population completed!')
        )
