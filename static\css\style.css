/* Merged styles from styles.css and team.css, with added styles for new hero section */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

body {
  font-family: "Poppins", sans-serif;
  color: #333;
  margin: 0;
  overflow-x: hidden;
  background-color: #ffffff;
}

/* Global heading font styles */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Poppins", sans-serif !important;
  font-weight: 800 !important;
}

/* Global number and counter styles */
.counter,
.stat-number,
.number,
[class*="number"],
[class*="counter"],
[class*="stat"] {
  font-weight: 900 !important;
}

/* Global text styles - Poppins Regular */
a,
p,
label,
.faq-answer,
.faq-answer p,
.about-description p,
.storytellers-content p,
.premium-service-desc,
.quote,
.testimonial-box p,
.member-bio,
.project-description,
.specialty-desc,
.video-info p,
.project-details .description,
.footer-text,
.cta-description,
span:not([class*="number"]):not([class*="counter"]):not([class*="stat"]) {
  font-family: "Poppins", sans-serif !important;
  font-weight: 400 !important;
}

/* Specific overrides for navigation and button text */
.nav-link,
.navbar-nav a,
.footer-link,
.btn-text,
.link-text,
.description,
.subtitle:not(.premium-subtitle),
.subheading,
.bio,
.content-text {
  font-family: "Poppins", sans-serif !important;
  font-weight: 400 !important;
}

/* Modern Cylindrical Navigation Bar */
.modern-navbar {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  width: auto;
  max-width: 90vw;
}

/* Hero Section - Original Blur Transparent Navbar */
.hero-section .modern-navbar {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.hero-section .nav-link {
  color: #ffb600;
}

.hero-section .nav-link:hover {
  color: rgb(0, 0, 0);
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.hero-section .nav-link.active {
  color: rgb(0, 0, 0);
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hero-section .navbar-cta .cta-button {
  background: #ffb600;
  color: #333;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.hero-section .navbar-cta .cta-button:hover {
  background: rgba(255, 255, 255, 1);
  color: #000;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.hero-section .modern-navbar.scrolled {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.hero-section .mobile-menu-toggle span {
  background: rgba(255, 255, 255, 0.9);
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1.5rem;
  gap: 2rem;
}

/* Logo Section */
.navbar-logo .logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.navbar-logo .logo-img {
  height: 35px;
  width: auto;
  transition: transform 0.3s ease;
  filter: brightness(0) invert(1);
  opacity: 0.9;
}

.navbar-logo .logo-img:hover {
  transform: scale(1.05);
  opacity: 1;
}

/* Navigation Links */
.navbar-nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-links {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 0.25rem;
}

.nav-link {
  text-decoration: none;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 0.9rem;
  padding: 0.6rem 1.2rem;
  border-radius: 20px;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
}

.nav-link:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.nav-link.active {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* CTA Button */
.navbar-cta .cta-button {
  background: #ffb600;
  color: #000;
  text-decoration: none;
  padding: 0.7rem 1.4rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 182, 0, 0.5);
  white-space: nowrap;
}

.navbar-cta .cta-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(255, 182, 0, 0.4);
  background: #ffffff;
  color: #000;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
  z-index: 99999;
  position: relative;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  /* Ensure it's always clickable */
  pointer-events: auto;
  /* Make sure it has proper dimensions */
  min-width: 40px;
  min-height: 40px;
  justify-content: center;
  align-items: center;
  /* Move it slightly to the left */
  margin-right: 10px;
}

.mobile-menu-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* Mobile menu animation */
.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation Menu */
.mobile-nav-menu {
  position: fixed;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  width: 90vw;
  max-width: 400px;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 99999;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%) translateY(-20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-nav-menu.active {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

.mobile-nav-links {
  list-style: none;
  margin: 0;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.mobile-nav-links li {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav-links li:last-child {
  border-bottom: none;
}

.mobile-nav-links .nav-link {
  display: block;
  padding: 15px 10px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.mobile-nav-links .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

/* Tablet Responsive */
@media (max-width: 1024px) {
  .modern-navbar {
    max-width: 92vw;
  }

  .navbar-container {
    padding: 0.7rem 1.3rem;
    gap: 1.5rem;
  }

  .nav-links {
    gap: 1.2rem;
  }

  .nav-link {
    font-size: 14px;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .modern-navbar {
    top: 10px;
    max-width: 95vw;
  }

  .navbar-container {
    padding: 0.6rem 1rem;
    gap: 1rem;
  }

  .navbar-nav {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .navbar-logo .logo-img {
    height: 30px;
  }

  .navbar-cta .cta-button {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .modern-navbar {
    top: 8px;
    max-width: 98vw;
  }

  .navbar-container {
    padding: 0.5rem 0.8rem;
    gap: 0.8rem;
  }

  .navbar-logo .logo-img {
    height: 28px;
  }

  .navbar-cta .cta-button {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }

  .mobile-nav-menu {
    top: 70px;
    width: 95vw;
  }

  /* Ensure mobile menu toggle is always visible and clickable */
  .mobile-menu-toggle {
    display: flex !important;
    z-index: 99999 !important;
    pointer-events: auto !important;
    opacity: 1 !important;
    visibility: visible !important;
  }

  /* Fix mobile menu positioning */
  .mobile-nav-menu {
    z-index: 99998 !important;
  }

  /* Force mobile menu toggle to be visible on all small screens */
  .navbar-nav {
    display: none !important;
  }

  .mobile-menu-toggle {
    display: flex !important;
  }
}

/* Scrolled state */
.modern-navbar.scrolled {
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
}

/* Main Content Area */
main {
  padding: 20px 30px;
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  padding: 0 50px;
  background: var(--hero-bg-color, #000);
  color: var(--hero-text-color, white);
  overflow: hidden;
}

.hero-container {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: flex-start;
}

.hero-content {
  flex: 0 0 auto;
  max-width: 600px;
  margin-left: 120px;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  justify-content: center;
  position: relative;
  z-index: 3;
}

.hero-logo-container {
  margin-bottom: 20px;
}

.hero-logo {
  width: 420px;
  height: auto;
  margin-bottom: 1px;
  /* Logo Animation Styles */
  opacity: 1;
  transform: scale(1) translateY(0);
  clip-path: inset(0 0 0 0);

  /* Sequenced animations */
  animation: wipeReveal 1s ease-out forwards,
    scaleRise 0.5s ease-out 1s forwards, logoGlow 3s ease-in-out 2.5s infinite;

  filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.2));
  transition: filter 0.3s ease;
  margin-top: 0;
  margin-left: 0;
  margin-bottom: 0;
  margin-right: 0;
}

/* Hero CTA Button Styles */
.hero-cta-btn {
  background-color: #1a1a1a;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 16px 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-family: "Arial", sans-serif;
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  min-width: 200px;
  height: 56px;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
  z-index: 1000;
  align-self: center;
  margin-top: 0px;
  margin-left: -280px;
  margin-bottom: 0;
  margin-right: -105px;
}

.hero-cta-btn:hover {
  min-width: 220px;
  padding: 16px 40px 16px 80px;
  box-shadow: 0 6px 25px rgba(255, 215, 0, 0.4);
  transform: scale(1.02);
}

/* CTA Icon Styles - N Logo comes from LEFT */
.cta-icon {
  position: absolute;
  left: -30px;
  opacity: 0;
  transform: translateX(-15px) rotate(-10deg) scale(0.8);
  transition: all 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  z-index: 3;
  filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));
}

.hero-cta-btn:hover .cta-icon {
  opacity: 1;
  left: 18px;
  transform: translateX(0) rotate(0deg) scale(1);
  filter: drop-shadow(0 4px 8px rgba(255, 215, 0, 0.5));
}

/* CTA Badge Styles - YOU comes from RIGHT */
.cta-badge {
  position: absolute;
  right: -60px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
  color: #1a1a1a;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 700;
  opacity: 0;
  transform: translateX(15px) scale(0.6) rotate(15deg);
  transition: all 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transition-delay: 0.15s;
  z-index: 3;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.hero-cta-btn:hover .cta-badge {
  opacity: 1;
  left: 46px;
  right: auto;
  transform: translateX(0) scale(1) rotate(0deg);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 215, 0, 0.4);
}

/* CTA Text Styles */
.cta-text {
  position: relative;
  white-space: nowrap;
  transition: all 0.4s ease-in-out;
  z-index: 1;
}

.cta-text.default-text {
  opacity: 1;
  transform: translateX(0);
}

.cta-text.hover-text {
  position: absolute;
  left: 0;
  top: 20px;
  opacity: 0;
  transform: translateX(20px);
  margin-left: 80px;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.hero-cta-btn:hover .cta-text.default-text {
  opacity: 0;
  transform: translateX(-20px);
}

.hero-cta-btn:hover .cta-text.hover-text {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.3s;
}

/* Animation keyframes for converging effect */
@keyframes bounceInFromLeft {
  0% {
    transform: translateX(-20px) scale(0.8) rotate(-10deg);
    opacity: 0;
  }
  60% {
    transform: translateX(5px) scale(1.1) rotate(2deg);
    opacity: 0.8;
  }
  100% {
    transform: translateX(0) scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes bounceInFromRight {
  0% {
    transform: translateX(20px) scale(0.6) rotate(15deg);
    opacity: 0;
  }
  60% {
    transform: translateX(-5px) scale(1.1) rotate(-2deg);
    opacity: 0.8;
  }
  100% {
    transform: translateX(0) scale(1) rotate(0deg);
    opacity: 1;
  }
}

.hero-cta-btn:hover .cta-icon {
  animation: bounceInFromLeft 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.hero-cta-btn:hover .cta-badge {
  animation: bounceInFromRight 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  animation-delay: 0.15s;
}

/* Background Text Styles */
.hero-bg-text {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  z-index: 0;
  pointer-events: none;
  opacity: 0.3;
  font-family: "Arial Black", Arial, sans-serif;
  font-weight: 900;
  font-size: 9rem;
  line-height: 0.75;
  color: var(--hero-text-color, #ffffff);
  text-align: left;
  white-space: nowrap;
  user-select: none;
  width: 55%;
  overflow: hidden;
}

.bg-text-row {
  display: block;
  margin: 0.2rem 0;
  letter-spacing: 0.5rem;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-duration: 15s;
}

.bg-text-row:nth-child(1) {
  animation-name: moveRight;
}

.bg-text-row:nth-child(2) {
  animation-name: moveLeft;
}

.bg-text-row:nth-child(3) {
  animation-name: moveRight;
}

.bg-text-row:nth-child(4) {
  animation-name: moveLeft;
}

.bg-text-row:nth-child(5) {
  animation-name: moveRight;
}

.bg-text-row:nth-child(6) {
  animation-name: moveLeft;
}

/* Hero Gallery */
.hero-gallery {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 15px;
  height: 90vh;
  width: 45%;
  overflow: hidden;
  padding-right: 50px;
  z-index: 2;
  mask: linear-gradient(
    to bottom,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%
  );
  -webkit-mask: linear-gradient(
    to bottom,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%
  );
}

/* Enhanced fade effects for hero gallery */
.hero-gallery::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 1) 0%,
    rgba(0, 0, 0, 0.7) 40%,
    transparent 100%
  );
  z-index: 10;
  pointer-events: none;
}

.hero-gallery::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 1) 0%,
    rgba(0, 0, 0, 0.7) 40%,
    transparent 100%
  );
  z-index: 10;
  pointer-events: none;
}

.gallery-column {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  position: relative;
  overflow: hidden;
}

.gallery-track {
  display: flex;
  flex-direction: column;
  gap: 15px;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  will-change: transform;
}

.gallery-column[data-direction="up"] .gallery-track {
  animation: heroScrollUp 500s linear infinite;
}

.gallery-column[data-direction="down"] .gallery-track {
  animation: heroScrollDown 500s linear infinite;
}

.gallery-img {
  width: 100%;
  height: 280px;
  object-fit: cover;
  border-radius: 12px;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  display: block;
}

.gallery-img:hover {
  transform: scale(1.03);
}

/* Hero Gallery Seamless Animations - Framer Style */
@keyframes heroScrollUp {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

@keyframes heroScrollDown {
  0% {
    transform: translateY(-50%);
  }
  100% {
    transform: translateY(0);
  }
}

/* Mobile Horizontal Scroll Animations */
@keyframes heroScrollLeft {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes heroScrollRight {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

/* Background Text Animations */
@keyframes moveLeft {
  0% {
    transform: translateX(55vw);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes moveRight {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(55vw);
  }
}

/* Logo Animations */
@keyframes wipeReveal {
  0% {
    clip-path: inset(0 100% 0 0);
    opacity: 0.8;
  }
  100% {
    clip-path: inset(0 0 0 0);
    opacity: 1;
  }
}

@keyframes scaleRise {
  0% {
    transform: scale(0.8) translateY(30px);
  }
  50% {
    transform: scale(1) translateY(0px);
  }
  100% {
    transform: scale(1) translateY(0);
  }
}

@keyframes logoGlow {
  0%,
  100% {
    filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.2));
  }
  50% {
    filter: drop-shadow(0 0 35px rgba(255, 215, 0, 0.5));
  }
}

/* Team Section */
.team {
  text-align: center;
  max-width: 100%;
  margin: 0 auto 40px auto;
  padding-top: 80px;
  background-color: #ffffff;
}
.team h1 {
  font-size: 4rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 32px;
  font-size: 36px;
  text-align: center;
  line-height: 1.1;
}

.team > p {
  font-size: 1.2rem;
  color: #666;
  line-height: 1.6;
  text-align: center;
  max-width: 700px;
  font-weight: 400;
  margin: 0 auto;
}

.center-line {
  display: block;
  text-align: center;
  width: 100%;
}

.storytellers-section {
  display: flex;
  gap: 30px;
  align-items: stretch;
  justify-content: center;
  background-color: #fff;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  max-width: 900px;
  margin: 40px auto 0 auto;
  width: 85%;
  min-height: 400px;
}

.storytellers-content {
  flex: 1;
}

.storytellers-content h2 {
  font-size: 28px;
  font-weight: 900;
  color: #1a1a1a;
  margin-bottom: 15px;
}

.storytellers-content p {
  font-size: 14px;
  color: #555;
  line-height: 1.6;
  margin-bottom: 20px;
  text-align: center;
  max-width: 600px;
  margin: 0 auto 20px auto;
}

.meet-btn {
  background-color: #1a1a1a;
  color: #fff;
  border: none;
  padding: 14px 28px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(26, 26, 26, 0.2);
}

.meet-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.6s;
}

.meet-btn span {
  margin-left: 10px;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-block;
}

.meet-btn:hover {
  background-color: #333;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(26, 26, 26, 0.3);
}

.meet-btn:hover::before {
  left: 100%;
}

.meet-btn:hover span {
  transform: translateX(5px);
}

.meet-btn:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s;
}

.action-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.case-study-btn {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  color: white;
  padding: 15px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: bold;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border: none;
  cursor: pointer;
  display: inline-block;
}

.case-study-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #ff5252, #26a69a);
}

.image-gallery {
  flex: 1.2;
  display: flex;
  gap: 16px;
  height: 500px;
  overflow: hidden;
  position: relative;
  border-radius: 20px;
}

/* Enhanced fade overlays for better visual effect */
.image-gallery::before,
.image-gallery::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 60px;
  z-index: 3;
  pointer-events: none;
}

.image-gallery::before {
  top: 0;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0.8) 30%,
    rgba(255, 255, 255, 0.3) 70%,
    transparent 100%
  );
}

.image-gallery::after {
  bottom: 0;
  background: linear-gradient(
    to top,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0.8) 30%,
    rgba(255, 255, 255, 0.3) 70%,
    transparent 100%
  );
}

.image-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  position: relative;
  will-change: transform;
  animation-play-state: running;
  animation-timing-function: linear;
}

/* Enhanced smooth animations like Framer website */
.column-1 {
  animation: smoothScrollUp 30s linear infinite;
}

.column-2 {
  animation: smoothScrollDown 35s linear infinite;
  animation-delay: -17.5s;
}

.image-column img {
  width: 100%;
  height: 180px;
  border-radius: 16px;
  object-fit: cover;
  flex-shrink: 0;
  display: block;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Hover effects for images */
.image-column img:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Enhanced smooth scrolling animations like Framer website */
@keyframes smoothScrollUp {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

@keyframes smoothScrollDown {
  0% {
    transform: translateY(-50%);
  }
  100% {
    transform: translateY(0);
  }
}

/* Pause animations on hover for better UX */
.image-gallery:hover .image-column {
  animation-play-state: paused;
}

/* Horizontal scrolling animations for mobile */
@keyframes horizontalScrollLeft {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes horizontalScrollRight {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

.trusted-by {
  text-align: center;
  margin-top: 40px;
  color: #777;
  font-weight: 500;
  overflow: hidden;
}

.trusted-by p {
  margin-bottom: 20px;
  font-size: 14px;
  text-align: center;
}

.logos {
  overflow: hidden;
  padding: 20px 0;
  background: #fff;
  white-space: nowrap;
  position: relative;
}

.logos::before,
.logos::after {
  content: "";
  position: absolute;
  top: 0;
  width: 60px;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

.logos::before {
  left: 0;
  background: linear-gradient(to right, #fff 60%, rgba(255, 255, 255, 0));
}

.logos::after {
  right: 0;
  background: linear-gradient(to left, #fff 60%, rgba(255, 255, 255, 0));
}

.logos-track {
  display: inline-flex;
  animation: seamlessLoop 30s linear infinite;
  align-items: center;
  gap: 40px;
  will-change: transform;
  width: max-content;
}

.logo-img {
  height: 35px;
  width: auto;
  opacity: 0.6;
  transition: all 0.3s ease;
  filter: grayscale(100%) brightness(0.8);
  flex-shrink: 0;
  object-fit: contain;
  min-width: 80px;
}

.logo-img:hover {
  opacity: 1;
  filter: grayscale(0%) brightness(1);
  transform: scale(1.05);
}

@keyframes seamlessLoop {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .logos {
    padding: 15px 0;
  }

  .logos::before,
  .logos::after {
    width: 30px;
  }

  .logos-track {
    gap: 30px;
    animation-duration: 25s;
  }

  .logo-img {
    height: 28px;
    min-width: 60px;
  }
}

@media (max-width: 480px) {
  .logos {
    padding: 10px 0;
  }

  .logos::before,
  .logos::after {
    width: 20px;
  }

  .logos-track {
    gap: 25px;
    animation-duration: 20s;
  }

  .logo-img {
    height: 24px;
    min-width: 50px;
  }
}

/* Additional styles for other sections */
section {
  padding: 40px 30px;
}

section h1 {
  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
  text-align: center;
  margin-bottom: 20px;
}

section h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  text-align: center;
  margin-bottom: 15px;
}

section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-top: -40px;
  text-align: right;
  margin-bottom: 20px;
  margin-right: 90px;
}
section h6 {
  font-size: 15px;
  font-weight: 700;
  color: #ffb600;
  text-align: center;
  margin-bottom: 20px;
}

section p {
  font-size: 14px;
  color: #000000;
  line-height: 1.6;
  text-align: left;
}

/* Enhanced About Section Styles */
#about {
  background-color: #ffffff;
  text-align: center;
  padding: 60px 30px 80px;
}

#about h1 {
  font-size: 2.5rem;
  margin-top: 0;
  color: #333;
  font-weight: 800;
  margin-bottom: 10px;
}

#about .subheading {
  font-size: 1rem;
  color: #666;
  margin-bottom: 40px;
  font-weight: 400;
}

#about .video-container {
  position: relative;
  width: 90%;
  max-width: 1000px;
  margin: 0 auto 60px;
  height: 0;
  padding-bottom: 50.625%; /* 16:9 aspect ratio for 90% width container */
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

#about .video-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

#about video,
#about iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 15px;
  border: none;
  object-fit: cover;
  transition: all 0.3s ease;
}

.about-description {
  max-width: 800px;
  margin: 0 auto 40px;
  text-align: center;
}

.about-description p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.counter-section {
  display: flex;
  justify-content: center;
  gap: 60px;
  flex-wrap: wrap;
  padding: 40px 20px 0;
}

.counter-box {
  min-width: 120px;
}

.counter {
  font-size: 2.5rem;
  color: #111;
  font-family: "Inter", sans-serif;
  font-weight: 900;
  margin-bottom: 10px;
}

.label {
  font-size: 1rem;
  color: #666;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* About Section Responsive Styles */
@media (max-width: 1200px) {
  #about .video-container {
    width: 95%;
    max-width: 900px;
  }
}

@media (max-width: 768px) {
  #about {
    padding: 40px 20px 60px;
  }

  #about h1 {
    font-size: 2rem;
  }

  #about .subheading {
    font-size: 0.9rem;
    margin-bottom: 30px;
  }

  #about .video-container {
    width: 98%;
    max-width: none;
    padding-bottom: 55.125%; /* Adjusted for wider mobile view */
    margin-bottom: 40px;
  }

  .about-description {
    padding: 0 10px;
  }

  .about-description p {
    font-size: 1rem;
  }

  .counter-section {
    gap: 40px;
    padding: 30px 20px 0;
  }

  .counter {
    font-size: 2rem;
  }

  .label {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  #about {
    padding: 30px 10px 40px;
  }

  #about h1 {
    font-size: 1.8rem;
  }

  #about .video-container {
    width: 100%;
    padding-bottom: 56.25%; /* Maintain 16:9 even on small screens for better video experience */
    margin-bottom: 30px;
    border-radius: 10px;
  }

  .counter-section {
    gap: 30px;
    flex-direction: column;
    align-items: center;
  }

  .counter-box {
    text-align: center;
    min-width: auto;
  }

  .counter {
    font-size: 1.8rem;
  }
}

/* Extra responsive breakpoint for very large screens */
@media (min-width: 1400px) {
  #about .video-container {
    width: 85%;
    max-width: 1200px;
  }
}

/* Premium Services Section */
#services-ticker {
  position: relative;
  background: #fafafa;
  padding: 100px 0;
  min-height: auto;
}

.premium-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.premium-header {
  text-align: center;
  margin-bottom: 80px;
}

.premium-title {
  font-size: 3rem;
  font-weight: 800;
  color: #1a1a1a;
  margin-bottom: 15px;
  letter-spacing: -0.02em;
}

.premium-subtitle {
  font-size: 1.1rem;
  color: #666;
  font-weight: 400;
  max-width: 500px;
  margin: 0 auto;
  line-height: 1.5;
}

.premium-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 40px;
  margin-bottom: 60px;
  justify-items: center;
  align-items: stretch;
  max-width: 1200px;
  margin: 0 auto 60px auto;
  padding: 0 20px;
}

.premium-card {
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e5e5;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

.premium-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #1a1a1a;
}

.premium-card:hover .premium-icon {
  background: #ffb600;
  border-color: #ffb600;
  transform: translateY(-2px);
}

.premium-card:hover .premium-icon img {
  opacity: 1;
  filter: grayscale(0%);
  transform: scale(1.1);
}

.premium-card-inner {
  padding: 40px 30px;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex: 1;
}

.premium-icon {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  width: 80px;
  margin: 0 auto 24px auto;
  background: #f8f9fa;
  border-radius: 20px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.premium-icon img {
  width: 48px;
  height: 48px;
  object-fit: contain;
  opacity: 0.8;
  transition: all 0.3s ease;
  filter: grayscale(0%);
}

.premium-service-title {
  font-size: 1.35rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 12px;
  line-height: 1.3;
  text-align: center;
  letter-spacing: -0.02em;
}

.premium-service-desc {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.premium-hover-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: #1a1a1a;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-card:hover .premium-hover-line {
  transform: scaleX(1);
}

/* Service Tooltip Styles */
.service-tooltip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  visibility: hidden;
  z-index: 1000;
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-card:hover .service-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translate(-50%, -50%) translateY(-5px);
}

.tooltip-content {
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  color: white;
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 8px 16px rgba(0, 0, 0, 0.2);
  max-width: 320px;
  min-width: 280px;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #ffb600;
  margin: 0 0 8px 0;
  letter-spacing: -0.01em;
}

.tooltip-description {
  font-size: 0.9rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-weight: 400;
}

.tooltip-arrow {
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 12px;
  height: 12px;
  background: rgba(26, 26, 26, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-top: none;
  border-left: none;
  transform: translateX(-50%) rotate(45deg);
}

/* Enhanced hover effects for premium cards with tooltips */
.premium-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  border-color: #ffb600;
}

.premium-card:hover .premium-card-inner {
  background: rgba(255, 182, 0, 0.02);
}

.premium-cta {
  text-align: center;
}

.premium-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 15px 30px;
  background: #1a1a1a;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  letter-spacing: 0.01em;
}

.premium-btn:hover {
  background: #333;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(26, 26, 26, 0.2);
}

.premium-btn-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.premium-btn:hover .premium-btn-arrow {
  transform: translateX(3px);
}

/* Premium Section Responsive Styles */
@media (max-width: 768px) {
  .premium-container {
    padding: 0 20px;
  }

  .premium-header {
    margin-bottom: 60px;
  }

  .premium-title {
    font-size: 2.2rem;
  }

  .premium-subtitle {
    font-size: 1rem;
  }

  .premium-grid {
    grid-template-columns: 1fr;
    gap: 25px;
    margin-bottom: 50px;
    padding: 0 15px;
  }

  .premium-card-inner {
    padding: 35px 25px;
  }

  .premium-icon {
    height: 70px;
    width: 70px;
    margin-bottom: 20px;
  }

  .premium-icon img {
    width: 40px;
    height: 40px;
  }

  .premium-service-title {
    font-size: 1.2rem;
  }

  .premium-service-desc {
    font-size: 0.9rem;
  }

  /* Tooltip responsive styles for tablet */
  .tooltip-content {
    max-width: 280px;
    min-width: 250px;
    padding: 18px 20px;
  }

  .tooltip-title {
    font-size: 1rem;
  }

  .tooltip-description {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  #services-ticker {
    padding: 60px 0;
  }

  .premium-container {
    padding: 0 15px;
  }

  .premium-title {
    font-size: 1.8rem;
  }

  .premium-card-inner {
    padding: 30px 20px;
  }

  .premium-icon {
    height: 60px;
    width: 60px;
    margin-bottom: 16px;
  }

  .premium-icon img {
    width: 36px;
    height: 36px;
  }

  /* Mobile tooltip styles */
  .service-tooltip {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .tooltip-content {
    max-width: 90vw;
    min-width: 280px;
    padding: 16px 18px;
    margin: 0 10px;
  }

  .tooltip-title {
    font-size: 0.95rem;
    margin-bottom: 6px;
  }

  .tooltip-description {
    font-size: 0.8rem;
    line-height: 1.4;
  }

  .tooltip-arrow {
    display: none; /* Hide arrow on mobile for cleaner look */
  }

  /* Touch-friendly hover for mobile */
  .premium-card:active .service-tooltip {
    opacity: 1;
    visibility: visible;
  }

  .premium-btn {
    padding: 12px 25px;
    font-size: 0.95rem;
  }
}

/* Framer-style Continuous scrolling testimonials */
.testimonial-section {
  overflow: hidden;
  position: relative;
  width: 100%;
  background-color: #f8f9fa;
  padding: 40px 0;
}

.testimonial-track {
  display: flex;
  animation: testimonialScroll 40s linear infinite;
  width: fit-content;
  gap: 24px;
  will-change: transform;
}

/* Pause animation on hover for better UX */
.testimonial-section:hover .testimonial-track {
  animation-play-state: paused;
}

@keyframes testimonialScroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.testimonial-box {
  flex: 0 0 350px;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.testimonial-box::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ffb600, #ff8c00);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.testimonial-box:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 182, 0, 0.3);
}

.testimonial-box:hover::before {
  transform: scaleX(1);
}

/* Ensure testimonial content is properly spaced */
.testimonial-box .flex {
  margin-bottom: 16px;
}

.profile-img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 18px;
  border: 3px solid rgba(255, 182, 0, 0.1);
  transition: all 0.3s ease;
}

.testimonial-box:hover .profile-img {
  border-color: rgba(255, 182, 0, 0.4);
  transform: scale(1.05);
}

.name {
  font-size: 19px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 6px;
  letter-spacing: -0.01em;
}

.title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 18px;
  font-weight: 500;
}

.quote {
  font-size: 16px;
  line-height: 1.65;
  color: #374151;
  font-weight: 400;
  letter-spacing: -0.01em;
}

.quote::before {
  content: '"';
  font-size: 24px;
  color: rgba(255, 182, 0, 0.6);
  font-weight: 700;
  line-height: 1;
  margin-right: 4px;
}

.quote::after {
  content: '"';
  font-size: 24px;
  color: rgba(255, 182, 0, 0.6);
  font-weight: 700;
  line-height: 1;
  margin-left: 4px;
}

/* Enhanced gradient fade effects for Framer-style */
.testimonial-section::before,
.testimonial-section::after {
  content: "";
  position: absolute;
  top: 0;
  height: 100%;
  width: 150px;
  pointer-events: none;
  z-index: 10;
}

.testimonial-section::before {
  left: 0;
  background: linear-gradient(
    to right,
    #f8f9fa 0%,
    rgba(248, 249, 250, 0.9) 40%,
    rgba(248, 249, 250, 0) 100%
  );
}

.testimonial-section::after {
  right: 0;
  background: linear-gradient(
    to left,
    #f8f9fa 0%,
    rgba(248, 249, 250, 0.9) 40%,
    rgba(248, 249, 250, 0) 100%
  );
}

/* Keep original fade classes for backward compatibility */
.fade-left,
.fade-right {
  display: none;
}

/* Responsive testimonial adjustments */
/* Large Desktop */
@media (min-width: 1200px) {
  .testimonial-section {
    padding: 50px 0;
  }

  .testimonial-box {
    flex: 0 0 380px;
    padding: 28px;
    min-height: 220px;
  }

  .testimonial-section::before,
  .testimonial-section::after {
    width: 180px;
  }
}

/* Desktop */
@media (max-width: 1199px) and (min-width: 1024px) {
  .testimonial-section {
    padding: 45px 0;
  }

  .testimonial-box {
    flex: 0 0 360px;
    padding: 26px;
    min-height: 210px;
  }

  .testimonial-section::before,
  .testimonial-section::after {
    width: 160px;
  }
}

/* Tablet */
@media (max-width: 1023px) and (min-width: 769px) {
  .testimonial-section {
    padding: 35px 0;
  }

  .testimonial-box {
    flex: 0 0 320px;
    padding: 22px;
    min-height: 190px;
  }

  .testimonial-section::before,
  .testimonial-section::after {
    width: 120px;
  }

  .testimonial-track {
    animation-duration: 38s;
  }

  .profile-img {
    width: 58px;
    height: 58px;
    margin-right: 16px;
  }

  .name {
    font-size: 18px;
  }

  .quote {
    font-size: 15px;
    line-height: 1.6;
  }
}

/* Mobile Large */
@media (max-width: 768px) and (min-width: 481px) {
  .testimonial-section {
    padding: 30px 0;
  }

  .testimonial-section::before,
  .testimonial-section::after {
    width: 100px;
  }

  .testimonial-box {
    flex: 0 0 300px;
    padding: 20px;
    min-height: 180px;
  }

  .testimonial-track {
    animation-duration: 35s;
    gap: 20px;
  }

  .profile-img {
    width: 54px;
    height: 54px;
    margin-right: 15px;
  }

  .name {
    font-size: 17px;
    margin-bottom: 4px;
  }

  .title {
    font-size: 13px;
    margin-bottom: 16px;
  }

  .quote {
    font-size: 15px;
    line-height: 1.6;
  }
}

/* Mobile Small */
@media (max-width: 480px) {
  .testimonial-section {
    padding: 25px 0;
  }

  .testimonial-section::before,
  .testimonial-section::after {
    width: 60px;
  }

  .testimonial-box {
    flex: 0 0 280px;
    padding: 18px;
    min-height: 170px;
  }

  .testimonial-track {
    animation-duration: 32s;
    gap: 16px;
  }

  .profile-img {
    width: 50px;
    height: 50px;
    margin-right: 14px;
  }

  .name {
    font-size: 16px;
    margin-bottom: 3px;
  }

  .title {
    font-size: 12px;
    margin-bottom: 14px;
  }

  .quote {
    font-size: 14px;
    line-height: 1.55;
  }

  .quote::before,
  .quote::after {
    font-size: 20px;
  }
}

/* Extra Small Mobile */
@media (max-width: 360px) {
  .testimonial-section {
    padding: 20px 0;
  }

  .testimonial-section::before,
  .testimonial-section::after {
    width: 40px;
  }

  .testimonial-box {
    flex: 0 0 260px;
    padding: 16px;
    min-height: 160px;
  }

  .testimonial-track {
    animation-duration: 30s;
    gap: 14px;
  }

  .profile-img {
    width: 46px;
    height: 46px;
    margin-right: 12px;
  }

  .name {
    font-size: 15px;
  }

  .title {
    font-size: 11px;
  }

  .quote {
    font-size: 13px;
    line-height: 1.5;
  }
}

/* Testimonial Section Header Responsive Styles */
#testimonial {
  padding: 80px 16px !important;
}

#testimonial .text-center {
  margin-bottom: 64px !important;
}

#testimonial h2 {
  font-size: 2.5rem !important;
  line-height: 1.2 !important;
  margin-bottom: 16px !important;
}

#testimonial h6 {
  font-size: 2rem !important;
  line-height: 1.3 !important;
}

/* Tablet Header */
@media (max-width: 1023px) and (min-width: 769px) {
  #testimonial {
    padding: 60px 16px !important;
  }

  #testimonial .text-center {
    margin-bottom: 48px !important;
  }

  #testimonial h2 {
    font-size: 2.2rem !important;
  }

  #testimonial h6 {
    font-size: 1.8rem !important;
  }
}

/* Mobile Header */
@media (max-width: 768px) {
  #testimonial {
    padding: 50px 16px !important;
  }

  #testimonial .text-center {
    margin-bottom: 40px !important;
  }

  #testimonial h2 {
    font-size: 1.9rem !important;
    margin-bottom: 12px !important;
  }

  #testimonial h6 {
    font-size: 1.5rem !important;
  }
}

/* Small Mobile Header */
@media (max-width: 480px) {
  #testimonial {
    padding: 40px 12px !important;
  }

  #testimonial .text-center {
    margin-bottom: 32px !important;
  }

  #testimonial h2 {
    font-size: 1.6rem !important;
    margin-bottom: 10px !important;
  }

  #testimonial h6 {
    font-size: 1.3rem !important;
  }
}

/* Extra Small Mobile Header */
@media (max-width: 360px) {
  #testimonial {
    padding: 35px 10px !important;
  }

  #testimonial .text-center {
    margin-bottom: 28px !important;
  }

  #testimonial h2 {
    font-size: 1.4rem !important;
    margin-bottom: 8px !important;
  }

  #testimonial h6 {
    font-size: 1.1rem !important;
  }
}

/* FAQ Section Styles */
.faq-container {
  display: grid;
  grid-template-columns: 280px 1fr;
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
  gap: 20px;
  align-items: start;
  padding: 60px 40px;
}
.faq-left-section {
  grid-column: 1;
  text-align: left;
  padding-top: 0;
}
.faq-left-section h1 {
  font-size: 45px;
  font-weight: 900;
  color: #000;
  margin: 0;
  line-height: 0.9;
  letter-spacing: -0.02em;
  text-align: left;
  position: sticky;
  top: 60px;
  margin-top: -180px;
}
.faq-left-section h1 span {
  font-size: 12px;
  font-weight: 500;
  color: #000;
  letter-spacing: 0;
  display: block;
  margin-bottom: 8px;
  position: relative;
  top: 60px;
  left: -43px;
}
.faq-right-section {
  grid-column: 2;
  width: 100%;
  max-width: none;
  padding-right: 0;
}
.faq-item {
  border-bottom: 1px solid #e0e0e0;
  padding: 20px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  width: 100%;
}
.faq-item:first-child {
  padding-top: 20px;
}
.faq-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
  padding-left: 8px;
  padding-right: 8px;
  margin-left: -8px;
  margin-right: -8px;
  border-radius: 8px;
}
.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}
.faq-question:hover h2 {
  color: #333;
}
.faq-question h2 {
  font-size: 24px;
  font-weight: 800;
  margin: 0;
  color: #000;
  transition: color 0.2s ease;
  letter-spacing: -0.01em;
  line-height: 1.3;
}
.faq-toggle {
  font-size: 32px;
  color: #000;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-block;
  transform-origin: center;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: transparent;
  position: relative;
  font-weight: 300;
  line-height: 1;
}
.faq-toggle:hover {
  background: rgba(0, 0, 0, 0.05);
}
.faq-toggle.active {
  transform: rotate(180deg);
  background: rgba(0, 0, 0, 0.08);
}
.faq-toggle.active:hover {
  transform: rotate(180deg);
}
.faq-answer {
  max-height: 0;
  overflow: hidden;
  padding-top: 0;
  padding-bottom: 0;
  color: #000;
  font-size: 16px;
  line-height: 150%;
  font-weight: 400;
  letter-spacing: -0.04em;
  opacity: 0;
  transform: translateY(-10px);
  transition: max-height 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    padding-top 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    padding-bottom 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s,
    transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s;
  width: 95%;
  max-width: none;
}
.faq-answer.show {
  max-height: 300px;
  padding-top: 16px;
  padding-bottom: 0;
  opacity: 1;
  transform: translateY(0);
}

/* FAQ Section Responsive Styles */

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .faq-container {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 50px 30px;
  }

  .faq-left-section {
    grid-column: 1;
    text-align: center;
    padding-top: 0;
  }

  .faq-left-section h1 {
    font-size: 3rem;
    position: static;
    margin-top: 0;
    text-align: center;
  }

  .faq-left-section h1 span {
    position: static;
    left: 0;
    top: 0;
    text-align: center;
    margin-bottom: 15px;
  }

  .faq-right-section {
    grid-column: 1;
    margin-top: 20px;
  }

  .faq-question h2 {
    font-size: 1.4rem;
  }

  .faq-answer {
    font-size: 0.95rem;
  }
}

/* Mobile Large (481px - 767px) */
@media (max-width: 767px) and (min-width: 481px) {
  .faq-container {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 40px 25px;
  }

  .faq-left-section {
    text-align: center;
    padding-top: 0;
  }

  .faq-left-section h1 {
    font-size: 2.5rem;
    position: static;
    margin-top: 0;
    text-align: center;
    line-height: 1.1;
  }

  .faq-left-section h1 span {
    position: static;
    left: 0;
    top: 0;
    text-align: center;
    margin-bottom: 12px;
    font-size: 0.75rem;
  }

  .faq-right-section {
    margin-top: 15px;
  }

  .faq-item {
    padding: 18px 0;
  }

  .faq-question h2 {
    font-size: 1.25rem;
    line-height: 1.4;
    padding-right: 10px;
  }

  .faq-toggle {
    font-size: 28px;
    width: 28px;
    height: 28px;
  }

  .faq-answer {
    font-size: 0.9rem;
    line-height: 1.6;
    width: 100%;
  }

  .faq-answer.show {
    padding-top: 14px;
  }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
  .faq-container {
    grid-template-columns: 1fr;
    gap: 25px;
    padding: 30px 15px;
  }

  .faq-left-section {
    text-align: center;
    padding-top: 0;
  }

  .faq-left-section h1 {
    font-size: 2rem;
    position: static;
    margin-top: 0;
    text-align: center;
    line-height: 1.1;
  }

  .faq-left-section h1 span {
    position: static;
    left: 0;
    top: 0;
    text-align: center;
    margin-bottom: 10px;
    font-size: 0.7rem;
  }

  .faq-right-section {
    margin-top: 10px;
  }

  .faq-item {
    padding: 15px 0;
  }

  .faq-item:hover {
    padding-left: 5px;
    padding-right: 5px;
    margin-left: -5px;
    margin-right: -5px;
  }

  .faq-question h2 {
    font-size: 1.1rem;
    line-height: 1.4;
    padding-right: 8px;
  }

  .faq-toggle {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }

  .faq-answer {
    font-size: 0.85rem;
    line-height: 1.6;
    width: 100%;
  }

  .faq-answer.show {
    padding-top: 12px;
    max-height: 400px; /* Allow more height for mobile */
  }
}

/* Extra Small Mobile (below 320px) */
@media (max-width: 319px) {
  .faq-container {
    padding: 25px 10px;
    gap: 20px;
  }

  .faq-left-section h1 {
    font-size: 1.8rem;
  }

  .faq-left-section h1 span {
    font-size: 0.65rem;
  }

  .faq-question h2 {
    font-size: 1rem;
  }

  .faq-toggle {
    font-size: 22px;
    width: 22px;
    height: 22px;
  }

  .faq-answer {
    font-size: 0.8rem;
  }
}

/* CTA Section Styles */
.cta-section {
  background-color: #ffffff;
  padding: 80px 40px 40px 40px;
  margin-top: 0;
  position: relative;
}

.cta-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 0 120px 0;
  position: relative;
  z-index: 2;
}

.cta-content {
  flex: 1;
  margin-top: -300px;
}

.cta-heading {
  font-size: 45px;
  font-weight: 600;
  color: #000;
  margin: 0;
  line-height: 1.1;
  letter-spacing: -0.02em;
  text-align: left;
  max-width: 300px;
  margin-top: -10px;
}

.cta-section .cta-button {
  background-color: #1a1a1a;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 16px 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-family: "Arial", sans-serif;
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  min-width: 200px;
  height: 56px;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
  z-index: 1000;
  margin-top: -250px;
}

.cta-section .cta-button:hover {
  min-width: 220px;
  padding: 16px 40px 16px 80px;
  box-shadow: 0 6px 25px rgba(255, 215, 0, 0.4);
  transform: scale(1.02);
}

/* CTA Section Button Icon Styles */
.cta-section .cta-icon {
  position: absolute;
  left: -30px;
  opacity: 0;
  transform: translateX(-15px) rotate(-10deg) scale(0.8);
  transition: all 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  z-index: 3;
  filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));
}

.cta-section .cta-button:hover .cta-icon {
  opacity: 1;
  left: 18px;
  transform: translateX(0) rotate(0deg) scale(1);
  filter: drop-shadow(0 4px 8px rgba(255, 215, 0, 0.5));
}

/* CTA Section Button Badge Styles */
.cta-section .cta-badge {
  position: absolute;
  right: -60px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
  color: #1a1a1a;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 700;
  opacity: 0;
  transform: translateX(15px) scale(0.6) rotate(15deg);
  transition: all 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transition-delay: 0.15s;
  z-index: 3;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.cta-section .cta-button:hover .cta-badge {
  opacity: 1;
  left: 46px;
  right: auto;
  transform: translateX(0) scale(1) rotate(0deg);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 215, 0, 0.4);
}

/* CTA Section Button Text Styles */
.cta-section .cta-text {
  position: relative;
  white-space: nowrap;
  transition: all 0.4s ease-in-out;
  z-index: 1;
}

.cta-section .cta-text.default-text {
  opacity: 1;
  transform: translateX(0);
}

.cta-section .cta-text.hover-text {
  position: absolute;
  left: 0;
  top: 20px;
  opacity: 0;
  transform: translateX(20px);
  margin-left: 80px;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.cta-section .cta-button:hover .cta-text.default-text {
  opacity: 0;
  transform: translateX(-20px);
}

.cta-section .cta-button:hover .cta-text.hover-text {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.3s;
}

/* CTA Section Button Animation Effects */
.cta-section .cta-button:hover .cta-icon {
  animation: bounceInFromLeft 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.cta-section .cta-button:hover .cta-badge {
  animation: bounceInFromRight 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  animation-delay: 0.15s;
}

/* CTA Section Responsive Styles */

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .cta-section {
    padding: 60px 30px 30px 30px;
    margin-top: 40px;
  }

  .cta-container {
    flex-direction: column;
    gap: 40px;
    padding: 30px 0 80px 0;
    text-align: center;
  }

  .cta-content {
    margin-top: 0;
    text-align: center;
  }

  .cta-heading {
    font-size: 3rem;
    max-width: none;
    margin-top: 0;
  }

  .cta-section .cta-button {
    margin-top: 0;
    position: static;
  }
}

/* Mobile Large (481px - 767px) */
@media (max-width: 767px) and (min-width: 481px) {
  .cta-section {
    padding: 50px 25px 25px 25px;
    margin-top: 30px;
  }

  .cta-container {
    flex-direction: column;
    gap: 30px;
    padding: 25px 0 60px 0;
    text-align: center;
  }

  .cta-content {
    margin-top: 0;
    text-align: center;
  }

  .cta-heading {
    font-size: 2.5rem;
    max-width: none;
    margin-top: 0;
    line-height: 1.1;
  }

  .cta-section .cta-button {
    margin-top: 0;
    position: static;
    padding: 14px 28px;
    font-size: 13px;
    min-width: 180px;
  }

  .cta-section .cta-button:hover {
    min-width: 200px;
    padding: 14px 35px 14px 70px;
  }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
  .cta-section {
    padding: 40px 15px 20px 15px;
    margin-top: 20px;
  }

  .cta-container {
    flex-direction: column;
    gap: 25px;
    padding: 20px 0 50px 0;
    text-align: center;
  }

  .cta-content {
    margin-top: 0;
    text-align: center;
  }

  .cta-heading {
    font-size: 2rem;
    max-width: none;
    margin-top: 0;
    line-height: 1.1;
  }

  .cta-section .cta-button {
    margin-top: 0;
    position: static;
    padding: 12px 24px;
    font-size: 12px;
    min-width: 160px;
    border-radius: 40px;
  }

  .cta-section .cta-button:hover {
    min-width: 180px;
    padding: 12px 30px 12px 60px;
    transform: scale(1.01);
  }

  .cta-section .cta-icon {
    width: 24px;
    height: 24px;
  }

  .cta-section .cta-badge {
    width: 28px;
    height: 28px;
    font-size: 9px;
  }

  .cta-section .cta-button:hover .cta-icon {
    left: 15px;
  }

  .cta-section .cta-button:hover .cta-badge {
    left: 40px;
  }

  .cta-section .cta-text.hover-text {
    margin-left: 60px;
  }
}

/* Extra Small Mobile (below 320px) */
@media (max-width: 319px) {
  .cta-section {
    padding: 30px 10px 15px 10px;
  }

  .cta-container {
    gap: 20px;
    padding: 15px 0 40px 0;
  }

  .cta-heading {
    font-size: 1.8rem;
  }

  .cta-section .cta-button {
    padding: 10px 20px;
    font-size: 11px;
    min-width: 140px;
  }

  .cta-section .cta-button:hover {
    min-width: 160px;
    padding: 10px 25px 10px 50px;
  }

  .cta-section .cta-button:hover .cta-icon {
    left: 12px;
  }

  .cta-section .cta-button:hover .cta-badge {
    left: 35px;
  }

  .cta-section .cta-text.hover-text {
    margin-left: 50px;
  }
}

/* Footer Styles */
.footer {
  background-color: #ffffff;
  padding: 40px 40px 60px 40px;
  margin-top: 300px;
  /* border-top: 1px solid #e0e0e0; */
}

.footer-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  position: relative;
}

.footer-brand {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  pointer-events: none;
  opacity: 1;
  margin-top: -250px;
}

.brand-text {
  font-size: clamp(120px, 20vw, 280px);
  font-weight: 900;
  color: #000;
  margin: 0;
  line-height: 0.8;
  letter-spacing: -0.05em;
  text-align: center;
  white-space: nowrap;
  opacity: 1;
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer-label {
  font-size: 10px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 4px;
  display: block;
}

.footer-email {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  text-decoration: none;
  display: block;
}

.footer-email:hover {
  text-decoration: underline;
}

.footer-right {
  display: flex;
  gap: 60px;
  align-items: flex-start;
}

.footer-nav-links,
.footer-social-links {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.footer-nav-links .footer-label,
.footer-social-links .footer-label {
  font-size: 10px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
}

.footer-link {
  font-size: 14px;
  font-weight: 500;
  color: #000;
  text-decoration: none;
  line-height: 1.6;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: #666;
}

/* LinkedIn Link Styling */
.footer-social-links .footer-link[href*="linkedin"] {
  color: #000000;
  font-weight: 600;
  position: relative;
  transition: all 0.3s ease;
}

.footer-social-links .footer-link[href*="linkedin"]:hover {
  color: #000000;
  transform: translateY(-1px);
}

.footer-social-links .footer-link[href*="linkedin"]:before {
  margin-right: 4px;
  font-size: 0.9em;
}

/* Footer Label Styling */
.footer-label {
  display: block;
  font-size: 10px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.footer-copyright {
  font-size: 12px;
  color: #666;
  font-weight: 400;
}

.footer-framer-text {
  font-size: 12px;
  color: #666;
  font-weight: 400;
}

/* Footer Responsive Styles */
@media (max-width: 1024px) {
  .footer {
    padding: 30px 30px 50px 30px;
    margin-top: 200px;
  }

  .footer-container {
    max-width: 900px;
  }

  .footer-right {
    gap: 40px;
  }

  .footer-brand {
    margin-top: -200px;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 25px 20px 40px 20px;
    margin-top: 150px;
  }

  .footer-container {
    flex-direction: column;
    gap: 30px;
    align-items: center;
    text-align: center;
  }

  .footer-brand {
    position: relative;
    top: auto;
    left: auto;
    transform: none;
    margin-top: 0;
    margin-bottom: 20px;
    order: -1;
  }

  .footer-left {
    order: 1;
    align-items: center;
  }

  .footer-right {
    order: 2;
    flex-direction: column;
    gap: 25px;
    align-items: center;
  }

  .footer-navigation,
  .footer-socials {
    text-align: center;
  }

  .footer-nav-links,
  .footer-social-links {
    align-items: center;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    margin-top: 30px;
    padding-top: 15px;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 20px 15px 35px 15px;
    margin-top: 100px;
  }

  .footer-container {
    gap: 20px;
  }

  .footer-brand .brand-text {
    font-size: 2rem;
  }

  .footer-right {
    gap: 20px;
  }

  .footer-nav-links,
  .footer-social-links {
    gap: 8px;
  }

  .footer-link {
    font-size: 13px;
  }

  .footer-email {
    font-size: 14px;
  }

  .footer-bottom {
    margin-top: 20px;
    padding-top: 12px;
  }

  .footer-copyright,
  .footer-framer-text {
    font-size: 11px;
  }
}

/* Enhanced Projects Section Styles */
#projects {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: black;
  padding: 0;
}

.ticker-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* Black blur fade overlays for Projects Section */
.projects-fade-left,
.projects-fade-right {
  position: absolute;
  top: 0;
  width: 150px;
  height: 100%;
  z-index: 3;
  pointer-events: none;
}

.projects-fade-left {
  left: 0;
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(0, 0, 0, 0.7) 30%,
    rgba(0, 0, 0, 0.3) 70%,
    transparent 100%
  );
}

.projects-fade-right {
  right: 0;
  background: linear-gradient(
    to left,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(0, 0, 0, 0.7) 30%,
    rgba(0, 0, 0, 0.3) 70%,
    transparent 100%
  );
}

.ticker-row {
  width: 100%;
  height: 33.33vh;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.ticker-track-container {
  width: 100%;
  overflow: hidden;
  position: relative;
}

.ticker-track {
  display: flex;
  gap: 20px;
  width: max-content;
  will-change: transform;
}

.ticker-track img {
  height: 30vh;
  object-fit: cover;
  flex-shrink: 0;
  width: auto;
  border-radius: 15px;
}

/* Pause animation on hover for better UX */
.ticker-track:hover {
  animation-play-state: paused;
}

/* Animations for seamless infinite loop */
.ticker-left {
  animation: scroll-left 60s linear infinite;
}

.ticker-right {
  animation: scroll-right 60s linear infinite;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-right {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

/* Overlay styles */
.overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.overlay h1 {
  font-size: 3.5rem;
  font-weight: 900;
  max-width: 90%;
  margin-bottom: 30px;
}

.fancy-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 15px 40px;
  background-color: white;
  color: black;
  border-radius: 50px;
  font-size: 18px;
  font-family: "Inter", sans-serif;
  text-align: center;
  text-decoration: none;
  transition: background-color 0.3s ease;
  position: relative;
  overflow: hidden;
}

.fancy-button .text {
  transition: transform 0.3s ease;
}

.fancy-button .arrow {
  opacity: 0;
  transform: translateX(20px);
  transition: transform 0.3s ease, opacity 0.3s ease;
  position: relative;
}

.fancy-button:hover {
  background-color: #ffb700;
}

.fancy-button:hover .text {
  transform: translateX(-10px);
}

.fancy-button:hover .arrow {
  opacity: 1;
  transform: translateX(0);
}

/* Responsive styles for Team Section image gallery */
@media (max-width: 1000px) {
  .storytellers-section {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }

  /* Transform to 2-row horizontal ticker on mobile */
  .image-gallery {
    height: 240px;
    flex-direction: column;
    gap: 10px;
    overflow: hidden;
    position: relative;
    border-radius: 16px;
  }

  /* Replace vertical fade overlays with horizontal ones */
  .image-gallery::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100px;
    width: 30px;
    height: 100%;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 1) 0%,
      rgba(255, 255, 255, 0.8) 40%,
      rgba(255, 255, 255, 0.2) 80%,
      transparent 100%
    );
    z-index: 2;
    pointer-events: none;
  }

  .image-gallery::after {
    content: "";
    position: absolute;
    top: 0;
    right: 100px;
    width: 30px;
    height: 100%;
    background: linear-gradient(
      to left,
      rgba(255, 255, 255, 1) 0%,
      rgba(255, 255, 255, 0.8) 40%,
      rgba(255, 255, 255, 0.2) 80%,
      transparent 100%
    );
    z-index: 2;
    pointer-events: none;
    visibility: hidden;
  }

  /* Convert columns to horizontal ticker rows */
  .image-column {
    flex-direction: row;
    gap: 12px;
    flex: none;
    width: max-content;
    height: 110px;
    display: flex;
    align-items: center;
  }

  /* Ensure images are duplicated for seamless loop */
  .image-column::after {
    content: "";
    display: block;
    width: 12px;
  }

  /* Slower, smoother animations for better mobile experience */
  .column-1 {
    animation: horizontalScrollLeft 25s linear infinite;
  }

  .column-2 {
    animation: horizontalScrollRight 30s linear infinite;
    animation-delay: -15s;
  }

  .image-column img {
    width: 100px;
    height: 100px;
    border-radius: 12px;
    flex-shrink: 0;
    object-fit: cover;
  }
}

@media (max-width: 480px) {
  .storytellers-section {
    padding: 0px;
    gap: 15px;
  }

  .image-gallery {
    height: 200px;
    border-radius: 12px;
  }

  .image-gallery::before,
  .image-gallery::after {
    width: 25px;
  }

  .image-column {
    gap: 8px;
    height: 90px;
  }

  .image-column img {
    width: 85px;
    height: 85px;
    border-radius: 10px;
  }

  /* Slower animations for better mobile experience */
  .column-1 {
    animation: horizontalScrollLeft 20s linear infinite;
  }

  .column-2 {
    animation: horizontalScrollRight 25s linear infinite;
    animation-delay: -12.5s;
  }

  /* Ensure storytellers content is properly sized */
  .storytellers-content {
    text-align: center;
  }

  .storytellers-content h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }

  .storytellers-content p {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}

/* Team Page Styles */
.team-hero {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: white;
  padding: 120px 40px 80px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.team-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.team-hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto 60px;
}

.team-hero h1 {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #ffffff 0%, #ffb600 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.team-hero p {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #cccccc;
  margin-bottom: 0;
}

/* Featured Team Carousel */
.featured-team-carousel {
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
}

.featured-member-card {
  position: relative;
  width: 200px;
  height: 250px;
  border-radius: 20px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.featured-member-card:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.member-image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.member-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.featured-member-card:hover .member-image {
  transform: scale(1.1);
}

.member-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 30px 20px 20px;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.featured-member-card:hover .member-overlay {
  transform: translateY(0);
}

.member-overlay h3 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.member-overlay p {
  font-size: 0.9rem;
  color: #ffb600;
  margin: 0;
}

/* Team Stats Section */
.team-stats {
  background: #f8f9fa;
  padding: 80px 40px;
}

.stats-container {
  display: flex;
  justify-content: center;
  gap: 80px;
  flex-wrap: wrap;
  max-width: 1200px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
  min-width: 150px;
}

.stat-number {
  font-size: 3rem;
  font-weight: 900;
  color: #1a1a1a;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #1a1a1a 0%, #ffb600 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 1rem;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Team Sections */
.team-section {
  padding: 80px 40px;
  background: white;
}

.team-section:nth-child(even) {
  background: #f8f9fa;
}

.section-header {
  text-align: center;
  max-width: 600px;
  margin: 0 auto 60px;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 20px;
}

.section-header p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
}

/* Team Grid */
.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.leadership-grid {
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 40px;
  max-width: 1000px;
}

/* Team Member Cards */
.team-member-card {
  perspective: 1000px;
  height: 380px;
  position: relative;
  width: 100%;
  max-width: 350px;
  margin: 0 auto;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.team-member-card:hover {
  transform: translateY(-5px);
}

.leadership-card {
  height: 420px;
  max-width: 400px;
}

.member-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  cursor: pointer;
}

.team-member-card:hover .member-card-inner {
  transform: rotateY(180deg);
}

.member-front,
.member-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.member-front {
  background: white;
  display: flex;
  flex-direction: column;
}

.member-back {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: white;
  transform: rotateY(180deg);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.member-photo {
  width: 100%;
  height: 240px;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
  border-radius: 8px 8px 0 0;
}

.leadership-card .member-photo {
  height: 260px;
}

.member-info {
  padding: 25px 20px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.member-info h3 {
  font-size: 1.4rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.member-role {
  font-size: 1rem;
  color: #ffb600;
  font-weight: 600;
  margin-bottom: 5px;
}

.member-experience {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

/* Member Back Details */
.member-details {
  text-align: center;
  width: 100%;
}

.member-details h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 15px;
}

.member-bio {
  font-size: 1rem;
  line-height: 1.5;
  color: #cccccc;
  margin-bottom: 20px;
}

.member-specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-bottom: 25px;
}

.specialty-tag {
  background: rgba(255, 182, 0, 0.2);
  color: #ffb600;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(255, 182, 0, 0.3);
}

.member-social {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 50%;
  text-decoration: none;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-link:hover {
  background: #ffb600;
  color: #1a1a1a;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 182, 0, 0.3);
}

.social-link.linkedin:hover {
  background: #0077b5;
  color: white;
}

.social-link.twitter:hover {
  background: #1da1f2;
  color: white;
}

.social-link.github:hover {
  background: #333;
  color: white;
}

.social-link.behance:hover {
  background: #1769ff;
  color: white;
}

.social-link.dribbble:hover {
  background: #ea4c89;
  color: white;
}

.social-link.instagram:hover {
  background: linear-gradient(
    45deg,
    #f09433 0%,
    #e6683c 25%,
    #dc2743 50%,
    #cc2366 75%,
    #bc1888 100%
  );
  color: white;
}

/* Portfolio Stats on Team Cards */
.portfolio-stats {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 20px;
  padding: 15px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.portfolio-stats .stat {
  text-align: center;
}

.portfolio-stats .stat-number {
  display: block;
  font-size: 1.2rem;
  font-weight: 900;
  color: #ffb600;
  margin-bottom: 2px;
}

.portfolio-stats .stat-label {
  font-size: 0.8rem;
  color: #cccccc;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.view-portfolio-hint {
  font-size: 0.8rem;
  color: #ffb600;
  margin-top: 10px;
  font-weight: 500;
  opacity: 0.8;
}

/* Team Member Detail Page Styles */
.member-hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.5) 100%
  );
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 120px 40px 80px;
  color: white;
}

.member-profile {
  display: flex;
  gap: 60px;
  align-items: center;
  margin-bottom: 80px;
  text-align: center;
  flex-direction: column;
}

.profile-image {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  object-fit: cover;
  border: 5px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.profile-info h1 {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 15px;
  background: linear-gradient(135deg, #ffffff 0%, #ffb600 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
}

.profile-info .role {
  font-size: 1.5rem;
  color: #ffb600;
  font-weight: 600;
  margin-bottom: 10px;
  text-align: center;
}

.profile-info .experience {
  font-size: 1.1rem;
  color: #cccccc;
  margin-bottom: 25px;
  text-align: center;
}

.profile-info .bio {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #ffffff;
  margin-bottom: 30px;
  max-width: 600px;
  text-align: left;
  margin-left: auto;
  margin-right: auto;
}

.contact-links {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
}

.contact-btn {
  display: inline-block;
  padding: 12px 25px;
  background: rgba(255, 182, 0, 0.2);
  color: #ffb600;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 600;
  border: 2px solid rgba(255, 182, 0, 0.3);
  transition: all 0.3s ease;
}

.contact-btn:hover {
  background: #ffb600;
  color: #1a1a1a;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 182, 0, 0.3);
}

/* Portfolio Overview Stats */
.portfolio-overview {
  display: flex;
  gap: 60px;
  justify-content: center;
  flex-wrap: wrap;
}

.overview-stat {
  text-align: center;
  min-width: 120px;
}

.overview-stat .stat-number {
  font-size: 3rem;
  font-weight: 900;
  color: #ffb600;
  margin-bottom: 10px;
  display: block;
}

.overview-stat .stat-label {
  font-size: 1rem;
  color: #cccccc;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

/* Detail Page Sections */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.specialties-section,
.featured-projects-section,
.portfolio-gallery-section,
.video-portfolio-section,
.all-projects-section,
.related-members-section {
  padding: 80px 0;
}

.specialties-section {
  background: #f8f9fa;
}

.specialties-section h2,
.featured-projects-section h2,
.portfolio-gallery-section h2,
.video-portfolio-section h2,
.all-projects-section h2,
.related-members-section h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  text-align: center;
  margin-bottom: 60px;
}

/* Specialties Grid */
.specialties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.specialty-item {
  background: white;
  padding: 25px 20px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.specialty-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.specialty-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

/* Projects Grid */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.project-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.project-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
  transform: scale(1.1);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.5) 100%
  );
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-overlay h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-align: center;
}

.project-overlay p {
  font-size: 1rem;
  margin-bottom: 15px;
  text-align: center;
}

.project-link {
  display: inline-block;
  padding: 10px 20px;
  background: #ffb600;
  color: #1a1a1a;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.project-link:hover {
  background: #ffffff;
  transform: translateY(-2px);
}

.project-info {
  padding: 25px;
}

.project-info h4 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.project-client {
  font-size: 1rem;
  color: #ffb600;
  font-weight: 600;
  margin-bottom: 10px;
}

.project-description {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15px;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tech-tag {
  background: #f8f9fa;
  color: #1a1a1a;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid #e9ecef;
}

/* Portfolio Gallery Styles */
.category-filter {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 10px 20px;
  background: #f8f9fa;
  color: #1a1a1a;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
  background: #ffb600;
  color: #1a1a1a;
  border-color: #ffb600;
  transform: translateY(-2px);
}

.masonry-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.gallery-item {
  break-inside: avoid;
  margin-bottom: 30px;
}

.gallery-image {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.gallery-image:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.gallery-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
}

.gallery-image:hover img {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.5) 100%
  );
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  padding: 20px;
  text-align: center;
}

.gallery-image:hover .image-overlay {
  opacity: 1;
}

.image-overlay h4 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 10px;
}

.image-overlay p {
  font-size: 0.9rem;
  margin-bottom: 15px;
  line-height: 1.4;
}

.view-project {
  display: inline-block;
  padding: 8px 16px;
  background: #ffb600;
  color: #1a1a1a;
  text-decoration: none;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.view-project:hover {
  background: #ffffff;
  transform: translateY(-2px);
}

/* Video Portfolio Styles */
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
}

.video-item {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background: #000;
  overflow: hidden;
}

.video-container video,
.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: none;
}

.video-info {
  padding: 25px;
}

.video-info h4 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 10px;
}

.video-info p {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15px;
}

.duration,
.category {
  display: inline-block;
  padding: 5px 12px;
  background: #f8f9fa;
  color: #1a1a1a;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-right: 10px;
  border: 1px solid #e9ecef;
}

.duration {
  background: #ffb600;
  color: #1a1a1a;
  border-color: #ffb600;
}

/* Projects Timeline Styles */
.projects-timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.projects-timeline::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e9ecef;
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: 60px;
  width: 100%;
}

.timeline-item:nth-child(odd) .timeline-content {
  margin-left: 0;
  margin-right: 50%;
  padding-right: 40px;
}

.timeline-item:nth-child(even) .timeline-content {
  margin-left: 50%;
  margin-right: 0;
  padding-left: 40px;
}

.timeline-date {
  position: absolute;
  left: 50%;
  top: 20px;
  transform: translateX(-50%);
  background: #ffb600;
  color: #1a1a1a;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  z-index: 2;
}

.timeline-content {
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.timeline-content:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.project-summary {
  display: flex;
  gap: 20px;
  padding: 25px;
  align-items: flex-start;
}

.project-thumb {
  width: 120px;
  height: 80px;
  object-fit: cover;
  border-radius: 10px;
  flex-shrink: 0;
}

.project-details h4 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.project-details .client {
  font-size: 1rem;
  color: #ffb600;
  font-weight: 600;
  margin-bottom: 10px;
}

.project-details .description {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15px;
}

.project-links {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.project-btn {
  display: inline-block;
  padding: 8px 16px;
  background: #f8f9fa;
  color: #1a1a1a;
  text-decoration: none;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.project-btn:hover {
  background: #ffb600;
  border-color: #ffb600;
  transform: translateY(-2px);
}

.technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tech-badge {
  background: #f8f9fa;
  color: #1a1a1a;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid #e9ecef;
}

/* Related Members Styles */
.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.related-member-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.related-member-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.related-member-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.related-member-card .member-info {
  padding: 20px;
  text-align: center;
}

.related-member-card h4 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 5px;
}

.related-member-card p {
  font-size: 1rem;
  color: #ffb600;
  font-weight: 600;
  margin-bottom: 5px;
}

.related-member-card .experience {
  font-size: 0.9rem;
  color: #666;
}

/* Back to Team Button */
.back-to-team {
  padding: 40px 0;
  background: #f8f9fa;
  text-align: center;
}

.back-btn {
  display: inline-block;
  padding: 15px 30px;
  background: #1a1a1a;
  color: white;
  text-decoration: none;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: #ffb600;
  color: #1a1a1a;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 182, 0, 0.3);
}

/* Team Member Detail Page Responsive Styles */
@media (max-width: 768px) {
  .member-hero {
    min-height: 80vh;
  }

  .hero-content {
    padding: 80px 20px 60px;
  }

  .member-profile {
    flex-direction: column;
    gap: 30px;
    text-align: center;
    margin-bottom: 60px;
  }

  .profile-image {
    width: 200px;
    height: 200px;
  }

  .profile-info h1 {
    font-size: 2.5rem;
  }

  .profile-info .role {
    font-size: 1.2rem;
  }

  .profile-info .bio {
    font-size: 1rem;
  }

  .portfolio-overview {
    gap: 30px;
  }

  .overview-stat .stat-number {
    font-size: 2.5rem;
  }

  .container {
    padding: 0 20px;
  }

  .specialties-section h2,
  .featured-projects-section h2,
  .portfolio-gallery-section h2,
  .video-portfolio-section h2,
  .all-projects-section h2,
  .related-members-section h2 {
    font-size: 2rem;
  }

  .specialties-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .masonry-gallery {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }

  .video-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .video-container {
    padding-bottom: 56.25%; /* Maintain 16:9 aspect ratio on mobile */
  }

  .projects-timeline::before {
    left: 20px;
  }

  .timeline-item:nth-child(odd) .timeline-content,
  .timeline-item:nth-child(even) .timeline-content {
    margin-left: 40px;
    margin-right: 0;
    padding-left: 20px;
    padding-right: 0;
  }

  .timeline-date {
    left: 20px;
    transform: none;
  }

  .project-summary {
    flex-direction: column;
    gap: 15px;
  }

  .project-thumb {
    width: 100%;
    height: 150px;
  }

  .related-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .profile-image {
    width: 150px;
    height: 150px;
  }

  .profile-info h1 {
    font-size: 2rem;
  }

  .overview-stat .stat-number {
    font-size: 2rem;
  }

  .masonry-gallery {
    grid-template-columns: 1fr;
  }

  .category-filter {
    gap: 10px;
  }

  .filter-btn {
    padding: 8px 16px;
    font-size: 0.9rem;
  }

  .video-container {
    padding-bottom: 75%; /* Slightly taller aspect ratio for mobile */
  }

  .project-summary {
    padding: 20px;
  }

  .related-grid {
    grid-template-columns: 1fr;
  }
}

/* Team Page Responsive Styles */
@media (max-width: 1024px) {
  .team-grid {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 25px;
  }

  .leadership-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
  }

  .team-member-card {
    max-width: 300px;
  }

  .leadership-card {
    max-width: 350px;
  }
}

@media (max-width: 768px) {
  .team-hero {
    padding: 80px 20px 60px;
  }

  .team-hero h1 {
    font-size: 2.5rem;
  }

  .team-hero p {
    font-size: 1rem;
  }

  .featured-team-carousel {
    gap: 20px;
  }

  .featured-member-card {
    width: 180px;
    height: 220px;
  }

  .team-stats {
    padding: 60px 20px;
  }

  .stats-container {
    gap: 40px;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .team-section {
    padding: 60px 20px;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .team-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    padding: 0 15px;
  }

  .leadership-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
  }

  .team-member-card {
    height: 340px;
    max-width: 320px;
  }

  .leadership-card {
    height: 370px;
    max-width: 350px;
  }

  .member-photo {
    height: 190px;
  }

  .leadership-card .member-photo {
    height: 210px;
  }
}

@media (max-width: 480px) {
  .team-hero h1 {
    font-size: 2rem;
  }

  .featured-member-card {
    width: 160px;
    height: 200px;
  }

  .stats-container {
    gap: 30px;
  }

  .stat-number {
    font-size: 2rem;
  }

  .team-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 10px;
  }

  .leadership-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .team-member-card {
    height: 300px;
    max-width: 100%;
  }

  .leadership-card {
    height: 330px;
    max-width: 100%;
  }

  .member-photo {
    height: 170px;
  }

  .leadership-card .member-photo {
    height: 190px;
  }

  .member-info {
    padding: 20px 15px;
  }

  .member-back {
    padding: 20px;
  }

  .member-details h3 {
    font-size: 1.3rem;
  }

  .member-bio {
    font-size: 0.9rem;
  }

  .social-link {
    width: 35px;
    height: 35px;
    font-size: 0.7rem;
  }
}

/* Responsive styles for Projects Section fade overlays */
@media (max-width: 768px) {
  .projects-fade-left,
  .projects-fade-right {
    width: 80px;
  }
}

@media (max-width: 480px) {
  .projects-fade-left,
  .projects-fade-right {
    width: 60px;
  }
}

/* Responsive Design for Hero Section - Superside Style */

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
  .hero-section {
    padding: 0 60px;
  }

  .hero-content {
    margin-left: 140px;
    max-width: 650px;
  }

  .hero-gallery {
    width: 42%;
    padding-right: 60px;
  }
}

/* Desktop (1024px - 1199px) */
@media (max-width: 1199px) and (min-width: 1024px) {
  .hero-section {
    padding: 0 40px;
  }

  .hero-content {
    margin-left: 100px;
    max-width: 580px;
  }

  .hero-gallery {
    width: 45%;
    padding-right: 40px;
  }
}

/* Tablet Landscape (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .hero-section {
    padding: 0 30px;
    min-height: 90vh;
    display: flex;
    align-items: flex-start;
  }

  .hero-container {
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 40px;
    height: 100%;
    width: 100%;
  }

  .hero-content {
    flex: 0 0 auto;
    margin-left: 0 !important;
    max-width: 100%;
    text-align: center;
    align-items: center;
    order: 1;
    padding-top: 80px;
    width: 100%;
  }

  .hero-gallery {
    position: relative !important;
    right: auto !important;
    top: auto !important;
    transform: none !important;
    width: 100% !important;
    height: 50vh;
    gap: 12px;
    padding-right: 0 !important;
    order: 2;
    mask: linear-gradient(
      to bottom,
      transparent 0%,
      black 10%,
      black 90%,
      transparent 100%
    );
    -webkit-mask: linear-gradient(
      to bottom,
      transparent 0%,
      black 10%,
      black 90%,
      transparent 100%
    );
  }

  .hero-logo {
    max-width: 450px;
    width: 100%;
    height: auto;
  }

  .hero-cta-btn {
    margin-left: 0 !important;
  }

  .gallery-img {
    height: 220px;
  }

  .gallery-column[data-direction="up"] .gallery-track {
    animation: heroScrollUp 120s linear infinite;
  }

  .gallery-column[data-direction="down"] .gallery-track {
    animation: heroScrollDown 120s linear infinite;
  }
}

/* Mobile Portrait (481px - 767px) */
@media (max-width: 767px) and (min-width: 481px) {
  .hero-section {
    padding: 0 20px;
    min-height: 100vh;
    display: flex;
    align-items: flex-start;
  }

  .hero-container {
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: center !important;
    gap: 30px;
    height: 100%;
    width: 100%;
    padding-top: 100px;
  }

  .hero-content {
    flex: 0 0 auto;
    margin-left: 0 !important;
    max-width: 100%;
    text-align: center;
    align-items: center;
    order: 1;
    width: 100%;
  }

  .hero-gallery {
    position: relative !important;
    right: auto !important;
    top: auto !important;
    transform: none !important;
    width: 100% !important;
    height: 45vh;
    gap: 10px;
    padding-right: 0 !important;
    order: 2;
    mask: linear-gradient(
      to bottom,
      transparent 0%,
      black 15%,
      black 85%,
      transparent 100%
    );
    -webkit-mask: linear-gradient(
      to bottom,
      transparent 0%,
      black 15%,
      black 85%,
      transparent 100%
    );
  }

  .hero-logo {
    max-width: 350px;
    width: 100%;
    height: auto;
  }

  .hero-cta-btn {
    transform: scale(0.9);
    margin-left: 0 !important;
  }

  .gallery-column {
    flex: 1;
    height: 100%;
  }

  .gallery-img {
    height: 180px;
    border-radius: 8px;
  }

  .hero-gallery::before,
  .hero-gallery::after {
    height: 50px;
  }

  .gallery-column[data-direction="up"] .gallery-track {
    animation: heroScrollUp 90s linear infinite;
  }

  .gallery-column[data-direction="down"] .gallery-track {
    animation: heroScrollDown 110s linear infinite;
  }

  /* Hide text overlay on mobile for better readability */
  .text-overlay {
    display: none;
  }
}

/* All Mobile Devices - Universal Mobile Fix */
@media (max-width: 768px) {
  /* Prevent horizontal scroll */
  body {
    overflow-x: hidden !important;
  }

  .hero-section {
    overflow-x: hidden !important;
    width: 100vw !important;
    max-width: 100% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
    box-sizing: border-box !important;
  }

  .hero-section .hero-container {
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: center !important;
    gap: 30px;
    height: 100%;
    width: 100% !important;
    max-width: 100% !important;
    padding-top: 100px;
    padding-left: 0 !important;
    padding-right: 0 !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
  }

  .hero-section .hero-content {
    flex: 0 0 auto !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: 100% !important;
    width: 100% !important;
    text-align: center !important;
    align-items: center !important;
    order: 1 !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
  }

  .hero-section .hero-gallery {
    position: relative !important;
    right: auto !important;
    left: auto !important;
    top: auto !important;
    transform: none !important;
    width: 100% !important;
    max-width: 100% !important;
    height: 45vh !important;
    gap: 10px;
    padding-right: 0 !important;
    padding-left: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    order: 2 !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
    mask: linear-gradient(
      to bottom,
      transparent 0%,
      black 15%,
      black 85%,
      transparent 100%
    ) !important;
    -webkit-mask: linear-gradient(
      to bottom,
      transparent 0%,
      black 15%,
      black 85%,
      transparent 100%
    ) !important;
  }

  .hero-section .hero-cta-btn {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .hero-section .hero-logo {
    max-width: 350px;
    width: 100%;
    height: auto;
  }

  /* Ensure background text doesn't cause overflow */
  .hero-section .hero-bg-text {
    display: none !important;
  }

  .hero-section .bg-text-row {
    display: none !important;
  }

  /* Hide text overlay on mobile for better readability */
  .text-overlay {
    display: none;
  }
}

/* Global Mobile Overflow Prevention */
@media (max-width: 768px) {
  html {
    overflow-x: hidden !important;
  }

  body {
    overflow-x: hidden !important;
    width: 100% !important;
    max-width: 100vw !important;
  }

  * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  .container,
  .hero-container,
  .hero-section {
    overflow-x: hidden !important;
  }

  /* Prevent specific elements from causing overflow */
  .hero-bg-text,
  .bg-text-row {
    display: none !important;
  }

  /* Ensure all positioned elements stay within bounds */
  .hero-section * {
    max-width: 100vw !important;
  }
}

/* Mobile Small (320px - 480px) */
@media (max-width: 480px) {
  .hero-section {
    padding: 0 15px !important;
    min-height: 100vh;
    display: flex;
    align-items: flex-start;
    overflow-x: hidden !important;
    width: 100% !important;
    max-width: 100vw !important;
  }

  .hero-section .hero-container {
    padding-top: 90px;
    gap: 25px;
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
  }

  .hero-section .hero-gallery {
    height: 40vh !important;
    gap: 8px;
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
  }

  .hero-section .hero-logo {
    max-width: 280px;
  }

  .hero-section .hero-cta-btn {
    transform: scale(0.85);
    margin-top: 10px;
  }

  .gallery-column {
    flex: 1;
    height: 100%;
    max-width: 33.333% !important;
  }

  .gallery-img {
    height: 150px;
    border-radius: 6px;
    width: 100% !important;
    max-width: 100% !important;
  }

  .hero-gallery::before,
  .hero-gallery::after {
    height: 40px;
  }

  .gallery-column[data-direction="up"] .gallery-track {
    animation: heroScrollUp 70s linear infinite;
  }

  .gallery-column[data-direction="down"] .gallery-track {
    animation: heroScrollDown 80s linear infinite;
  }
}

/* Extra Small Mobile (below 320px) */
@media (max-width: 319px) {
  .hero-section {
    padding: 0 10px;
  }

  .hero-container {
    padding-top: 80px;
    gap: 20px;
  }

  .hero-logo {
    max-width: 240px;
  }

  .hero-gallery {
    height: 35vh;
    gap: 6px;
  }

  .gallery-img {
    height: 130px;
    border-radius: 4px;
  }

  .hero-cta-btn {
    transform: scale(0.8);
  }

  .gallery-column[data-direction="up"] .gallery-track {
    animation: heroScrollUp 60s linear infinite;
  }

  .gallery-column[data-direction="down"] .gallery-track {
    animation: heroScrollDown 70s linear infinite;
  }
}

/* Services Ticker Section Styles */
/* Scoped to avoid conflicts with existing website styles */

/* Background animation */
.services-ticker-section .ticker-bg-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.services-ticker-section .ticker-floating-orb {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, #ffffff1a 0%, transparent 70%);
  animation: ticker-float 8s ease-in-out infinite;
}

.services-ticker-section .ticker-orb-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.services-ticker-section .ticker-orb-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: -3s;
}

.services-ticker-section .ticker-orb-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 60%;
  animation-delay: -6s;
}

@keyframes ticker-float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
}

/* Section container */
.services-ticker-section {
  position: relative;
  width: 100%;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #ffffff 0%, #ffffff 50%, #ffffff 100%);
  color: #fff;
  font-family: "Inter", "Segoe UI", sans-serif;
  overflow: hidden;
}

/* Main container */
.services-ticker-section .ticker-main-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

/* Header section */
.services-ticker-section .ticker-header-section {
  text-align: center;
  margin-bottom: 3rem;
  animation: ticker-fadeInUp 1s ease-out;
}

.services-ticker-section .ticker-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.services-ticker-section .ticker-logo-icon {
  font-size: 2.5rem;
  animation: ticker-pulse 2s ease-in-out infinite;
}

.services-ticker-section .ticker-brand-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #1e2024, #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  color: #000;
}

.services-ticker-section .ticker-tagline {
  font-size: 1.1rem;
  color: #000000;
  font-weight: 300;
  letter-spacing: 0.5px;
  margin: 0;
}

/* Enhanced horizontal ticker wrapper */
.services-ticker-section .ticker-wrapper {
  position: relative;
  width: fit-content;
  max-width: 1200px;
  background: #ffffffcc;
  border-radius: 20px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid #ffffff;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: ticker-slideInUp 1s ease-out 0.3s both;
}

.services-ticker-section .ticker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.services-ticker-section .ticker-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
  margin: 0;
}

.services-ticker-section .ticker-indicator {
  display: flex;
  gap: 0.5rem;
}

.services-ticker-section .indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ffffff4d;
  transition: all 0.3s ease;
}

.services-ticker-section .indicator-dot.active {
  background: #ffb600;
  box-shadow: 0 0 10px #ffb600;
}

.services-ticker-section .ticker-container {
  height: 120px;
  width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 1;
  border-radius: 12px;
}

.services-ticker-section .ticker {
  display: flex;
  flex-direction: row;
  animation: ticker-scroll-left 20s linear infinite;
  width: fit-content;
}

.services-ticker-section .ticker-item {
  position: relative;
  min-width: 300px;
  width: 300px;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  margin-right: 1rem;
  font-size: 16px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(3, 0, 0, 0.485) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.4s ease;
  cursor: pointer;
  z-index: 10;
  backdrop-filter: blur(5px);
  flex-shrink: 0;
}

.services-ticker-section .ticker-item:hover {
  background: linear-gradient(
    135deg,
    rgba(0, 255, 200, 0.2) 0%,
    rgba(0, 168, 255, 0.2) 100%
  );
  transform: translateY(-10px) scale(1.05);
  border: 1px solid #ffb600;
  box-shadow: 0 10px 30px #33363c;
}

.services-ticker-section .ticker-item:hover .item-content {
  text-shadow: 0 2px 12px rgba(0, 0, 0, 1), 0 0 20px rgba(0, 0, 0, 0.8);
  z-index: 15;
  position: relative;
}

.services-ticker-section .ticker-item:hover .item-icon {
  text-shadow: 0 2px 12px rgba(0, 0, 0, 1), 0 0 20px rgba(0, 0, 0, 0.8);
  z-index: 15;
  position: relative;
}

.services-ticker-section .ticker-item:hover .item-arrow {
  text-shadow: 0 2px 12px rgba(0, 0, 0, 1), 0 0 20px rgba(0, 0, 0, 0.8);
  z-index: 15;
  position: relative;
}

.services-ticker-section .item-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  transition: transform 0.3s ease;
}

.services-ticker-section .ticker-item:hover .item-icon {
  transform: scale(1.2) rotate(10deg);
}

.services-ticker-section .item-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.services-ticker-section .item-title {
  font-weight: 600;
  font-size: 1rem;
  color: #fff;
}

.services-ticker-section .item-subtitle {
  font-size: 0.8rem;
  color: #888;
  font-weight: 300;
}

.services-ticker-section .item-arrow {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.2rem;
  color: #33363c;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
}

.services-ticker-section .ticker-item:hover .item-arrow {
  opacity: 1;
  transform: translateX(0);
}

@keyframes ticker-scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.services-ticker-section .ticker-mask {
  position: absolute;
  height: 100%;
  width: 40px;
  z-index: 3;
  top: 0;
}

.services-ticker-section .left-mask {
  left: 0;
  background: linear-gradient(to right, rgba(26, 26, 26, 1), transparent);
  border-radius: 12px 0 0 12px;
}

.services-ticker-section .right-mask {
  right: 0;
  background: linear-gradient(to left, rgba(26, 26, 26, 1), transparent);
  border-radius: 0 12px 12px 0;
}

/* Progress bar */
.services-ticker-section .progress-bar {
  position: absolute;
  bottom: 1rem;
  left: 1.5rem;
  right: 1.5rem;
  height: 3px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  visibility: hidden;
}

.services-ticker-section .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #000000, #ffffff);
  border-radius: 2px;
  animation: ticker-progress 20s linear infinite;
}

@keyframes ticker-progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

/* Enhanced preview overlay - emerges from text with tilt */
.services-ticker-section .preview {
  position: fixed;
  width: 480px;
  height: 360px;
  pointer-events: none;
  z-index: 1000;
  opacity: 0;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: scale(0.1) rotate(12deg);
  filter: blur(10px);
}

.services-ticker-section .preview.show {
  opacity: 0.85;
  transform: scale(1) rotate(-3deg);
  filter: blur(0px);
}

.services-ticker-section .preview-inner {
  width: 400px;
  height: 300px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(1.2);
  border-radius: 16px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.services-ticker-section .preview.show .preview-inner {
  transform: translate(-50%, -50%) scale(1);
}

.services-ticker-section .preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  filter: blur(15px) brightness(0.3) contrast(2);
  border-radius: 16px;
  transform: scale(1.5) rotate(15deg);
  opacity: 0.3;
}

.services-ticker-section .preview.show img {
  filter: blur(0px) brightness(1) contrast(1);
  transform: scale(1) rotate(-8deg);
  opacity: 1;
}

/* Hide all text overlays on the image */
.services-ticker-section .preview-overlay,
.services-ticker-section .preview-header,
.services-ticker-section .preview-close,
.services-ticker-section .preview-description,
.services-ticker-section .preview-content,
.services-ticker-section .preview-category,
.services-ticker-section .preview-title {
  display: none;
}

/* Animation keyframes */
@keyframes ticker-fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ticker-slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ticker-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .services-ticker-section {
    padding: 2rem 1rem;
  }

  .services-ticker-section .ticker-main-container {
    padding: 1rem 0;
  }

  .services-ticker-section .ticker-wrapper {
    width: 95%;
    padding: 1rem;
  }

  .services-ticker-section .ticker-container {
    height: 100px;
  }

  .services-ticker-section .ticker-item {
    min-width: 220px;
    width: 220px;
    height: 80px;
    padding: 0.8rem;
  }

  .services-ticker-section .item-icon {
    font-size: 1.5rem;
    margin-bottom: 0.3rem;
  }

  .services-ticker-section .item-title {
    font-size: 0.9rem;
  }

  .services-ticker-section .item-subtitle {
    font-size: 0.7rem;
  }

  .services-ticker-section .ticker-brand-title {
    font-size: 2rem;
  }

  .services-ticker-section .ticker-tagline {
    font-size: 1rem;
  }

  .services-ticker-section .preview {
    width: 350px;
    height: 250px;
  }

  .services-ticker-section .preview-inner {
    width: 300px;
    height: 200px;
  }

  .services-ticker-section .ticker-header h3 {
    font-size: 1.2rem;
  }
}

/* Cookies Banner Styles */
.cookies-banner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(26, 26, 26, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  color: #ffffff;
  padding: 20px;
  z-index: 10000;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.cookies-banner.show {
  transform: translateY(0);
}

.cookies-banner.hide {
  transform: translateY(100%);
}

.cookies-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  flex-wrap: wrap;
}

.cookies-content {
  flex: 1;
  min-width: 300px;
}

.cookies-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #ffffff;
}

.cookies-text {
  font-size: 0.95rem;
  color: #cccccc;
  line-height: 1.5;
  margin-bottom: 0;
}

.cookies-text a {
  color: #ffb600;
  text-decoration: none;
  font-weight: 500;
}

.cookies-text a:hover {
  text-decoration: underline;
}

.cookies-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-shrink: 0;
}

.cookies-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  min-width: 100px;
}

.cookies-btn-accept {
  background: #ffb600;
  color: #1a1a1a;
}

.cookies-btn-accept:hover {
  background: #e6a300;
  transform: translateY(-1px);
}

.cookies-btn-decline {
  background: transparent;
  color: #cccccc;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.cookies-btn-decline:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.cookies-btn-settings {
  background: transparent;
  color: #ffb600;
  border: 1px solid #ffb600;
  font-size: 0.85rem;
  padding: 8px 16px;
  min-width: auto;
}

.cookies-btn-settings:hover {
  background: #ffb600;
  color: #1a1a1a;
}

/* Cookies Settings Modal */
.cookies-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.cookies-modal.show {
  opacity: 1;
  visibility: visible;
}

.cookies-modal-content {
  background: #ffffff;
  border-radius: 15px;
  padding: 30px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.cookies-modal.show .cookies-modal-content {
  transform: scale(1);
}

.cookies-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.cookies-modal-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.cookies-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.cookies-modal-close:hover {
  background: #f0f0f0;
  color: #1a1a1a;
}

.cookies-category {
  margin-bottom: 25px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
}

.cookies-category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.cookies-category-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.cookies-toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.cookies-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.cookies-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.cookies-toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.cookies-toggle input:checked + .cookies-toggle-slider {
  background-color: #ffb600;
}

.cookies-toggle input:checked + .cookies-toggle-slider:before {
  transform: translateX(26px);
}

.cookies-toggle input:disabled + .cookies-toggle-slider {
  background-color: #4caf50;
  cursor: not-allowed;
}

.cookies-category-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

.cookies-modal-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.cookies-modal-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cookies-modal-btn-save {
  background: #ffb600;
  color: #1a1a1a;
}

.cookies-modal-btn-save:hover {
  background: #e6a300;
}

.cookies-modal-btn-cancel {
  background: #f0f0f0;
  color: #666;
}

.cookies-modal-btn-cancel:hover {
  background: #e0e0e0;
  color: #1a1a1a;
}

/* Responsive Design for Cookies */
@media (max-width: 768px) {
  .cookies-container {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .cookies-content {
    min-width: auto;
  }

  .cookies-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .cookies-btn {
    flex: 1;
    min-width: 120px;
  }

  .cookies-modal-content {
    padding: 20px;
    margin: 10px;
  }

  .cookies-modal-actions {
    flex-direction: column;
  }

  .cookies-modal-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .cookies-banner {
    padding: 15px;
  }

  .cookies-title {
    font-size: 1rem;
  }

  .cookies-text {
    font-size: 0.9rem;
  }

  .cookies-btn {
    padding: 8px 16px;
    font-size: 0.85rem;
  }
}
