# Generated manually for overlay logo fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0018_remove_navbarlink_parent_link_delete_navbarsettings_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='casestudy',
            name='overlay_logo',
            field=models.ImageField(blank=True, help_text='Logo overlay for second curved square image (appears on hover)', null=True, upload_to='casestudy/logos/'),
        ),
        migrations.AddField(
            model_name='casestudy',
            name='overlay_logo_alt',
            field=models.CharField(default='Overlay Logo', help_text='Alt text for overlay logo', max_length=200),
        ),
    ]
