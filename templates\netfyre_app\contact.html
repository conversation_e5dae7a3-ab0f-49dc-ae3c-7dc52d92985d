{% extends 'base.html' %} {% load static %} {% block title %}Contact - {{
site_settings.site_title }}{% endblock %} {% block extra_css %}
<link rel="stylesheet" href="{% static 'css/contact.css' %}" />
{% endblock %} {% block content %}
<div class="contact-container">
  <div class="left-section">
    <h1>Our Guarantee</h1>
    <p style="color: #666666e3" class="subtitle">
      If you don’t see at least a 30% increase in revenue, AOV, or conversion
      rate within 45 days, we’ll keep working for free until you do.
    </p>
    <p style="color: #666666e3" class="subtitle">Not a fan of forms</p>
    <p class="subtitle">Let's hop on a call instead.</p>
    <br />
    <button class="book-call-btn" onclick="handleBookCall()">
      BOOK A CALL
    </button>
  </div>

  <div class="right-section">
    <form
      class="contact-form"
      method="post"
      action="{% url 'netfyre_app:contact' %}"
    >
      {% csrf_token %} {% if messages %}
      <div class="messages">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">{{ message }}</div>
        {% endfor %}
      </div>
      {% endif %}

      <div class="form-row">
        <div class="form-group">
          <label for="fullname"
            >Full Name <span class="required">*</span></label
          >
          <input
            type="text"
            id="fullname"
            name="fullname"
            placeholder="Enter your full name"
            required
          />
        </div>

        <div class="form-group">
          <label for="website"
            >Website URL <span class="required">*</span></label
          >
          <input
            type="url"
            id="website"
            name="website"
            placeholder="Enter your website url"
            required
          />
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="phone"
            >Phone Number <span class="required">*</span></label
          >
          <input
            type="tel"
            id="phone"
            name="phone"
            placeholder="Enter your phone number"
            required
          />
        </div>

        <div class="form-group">
          <label for="email"
            >Email Address <span class="required">*</span></label
          >
          <input
            type="email"
            id="email"
            name="email"
            placeholder="Enter your email address"
            required
          />
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="revenue"
            >Monthly Revenue <span class="required">*</span></label
          >
          <select id="revenue" name="revenue" required>
            <option value="">Select one</option>
            <option value="0-10k">$0 - $10,000</option>
            <option value="10k-50k">$10,000 - $50,000</option>
            <option value="50k-100k">$50,000 - $100,000</option>
            <option value="100k+">$100,000+</option>
          </select>
        </div>

        <div class="form-group">
          <label for="source"
            >How did you hear about us? <span class="required">*</span></label
          >
          <select id="source" name="source" required>
            <option value="">Select one</option>
            <option value="google">Google Search</option>
            <option value="social">Social Media</option>
            <option value="referral">Referral</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group-full">
          <label for="business_type"
            >Business Type <span class="required">*</span></label
          >
          <select id="business_type" name="business_type" required>
            <option value="">Select your business type</option>
            <option value="ecommerce">E-commerce</option>
            <option value="saas">SaaS/Software</option>
            <option value="healthcare">Healthcare</option>
            <option value="finance">Finance/Banking</option>
            <option value="education">Education</option>
            <option value="real-estate">Real Estate</option>
            <option value="restaurant">Restaurant/Food</option>
            <option value="fitness">Fitness/Wellness</option>
            <option value="consulting">Consulting</option>
            <option value="manufacturing">Manufacturing</option>
            <option value="nonprofit">Non-profit</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>

      <button type="submit" class="submit-btn">SUBMIT</button>
    </form>
  </div>
</div>

<script>
  function handleBookCall() {
    // Scroll to contact form on same page
    document.querySelector(".contact-form").scrollIntoView({
      behavior: "smooth",
    });
  }
</script>
{% endblock %}
