// JavaScript from script.js
document.addEventListener("DOMContentLoaded", function () {
  const tickerTracks = document.querySelectorAll(".ticker-track");

  tickerTracks.forEach((track) => {
    const originalImages = Array.from(track.querySelectorAll("img"));
    track.innerHTML = "";
    originalImages.forEach((img) => {
      track.appendChild(img.cloneNode(true));
    });
    originalImages.forEach((img) => {
      track.appendChild(img.cloneNode(true));
    });
    track.offsetWidth;
  });

  // Initialize team section mobile ticker
  initializeTeamMobileTicker();

  // Reinitialize on window resize
  let resizeTimeout;
  window.addEventListener("resize", () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      initializeTeamMobileTicker();
    }, 250);
  });
});

// Team section mobile ticker initialization
function initializeTeamMobileTicker() {
  const imageColumns = document.querySelectorAll(
    ".storytellers-section .image-column"
  );

  imageColumns.forEach((column) => {
    const images = Array.from(column.querySelectorAll("img"));

    if (images.length > 0) {
      // Clone images for seamless infinite scroll
      images.forEach((img) => {
        const clone = img.cloneNode(true);
        column.appendChild(clone);
      });

      // Add additional clones for smoother animation on mobile
      if (window.innerWidth <= 768) {
        images.forEach((img) => {
          const clone = img.cloneNode(true);
          column.appendChild(clone);
        });
      }
    }
  });
}

// Counter Animation Script
const counters = document.querySelectorAll(".counter");
let started = false;

const runCounters = () => {
  counters.forEach((counter) => {
    counter.innerText = "0";
    const target = +counter.getAttribute("data-target");
    const prefix = counter.getAttribute("data-prefix") || "";
    const showPlus = counter.getAttribute("data-plus") === "true";

    const update = () => {
      const current = +counter.innerText.replace(/[^\d]/g, "");
      const increment = target / 200;

      if (current < target) {
        counter.innerText = `${prefix}${Math.ceil(current + increment)}`;
        setTimeout(update, 10);
      } else {
        counter.innerText = `${prefix}${target}${showPlus ? "+" : ""}`;
      }
    };

    update();
  });
};

const observer = new IntersectionObserver(
  (entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting && !started) {
        started = true;
        runCounters();
      }
    });
  },
  { threshold: 0.5 }
);

const counterSection = document.querySelector(".counter-section");
if (counterSection) {
  observer.observe(counterSection);
}

// FAQ Section JavaScript
document.querySelectorAll(".faq-question").forEach((item) => {
  item.addEventListener("click", () => {
    const answer = item.nextElementSibling;
    const toggle = item.querySelector(".faq-toggle");
    const isOpen = answer.classList.contains("show");

    // Toggle the clicked answer independently
    if (isOpen) {
      answer.classList.remove("show");
      toggle.classList.remove("active");
      // Small delay to sync with rotation animation
      setTimeout(() => {
        toggle.textContent = "+";
      }, 200);
    } else {
      answer.classList.add("show");
      toggle.classList.add("active");
      // Small delay to sync with rotation animation
      setTimeout(() => {
        toggle.textContent = "−";
      }, 200);
    }
  });
});

// Services Ticker functionality
class ServicesTicker {
  constructor() {
    this.section = document.getElementById("services-ticker");
    this.ticker = document.getElementById("services-ticker-element");
    this.preview = document.getElementById("services-preview");
    this.previewImg = this.preview.querySelector("img");
    this.indicators = this.section.querySelectorAll(".indicator-dot");

    this.currentIndex = 0;
    this.isHovered = false;
    this.animationPaused = false;

    this.init();
  }

  init() {
    // Clone ticker items for infinite horizontal scroll
    const tickerItems = this.ticker.querySelectorAll(".ticker-item");
    tickerItems.forEach((item) => {
      const clone = item.cloneNode(true);
      this.ticker.appendChild(clone);
    });

    // Setup event listeners
    this.setupHoverEvents();
    this.setupIndicators();
    this.startIndicatorAnimation();

    // Pause animation on hover
    this.ticker.addEventListener("mouseenter", () => {
      this.pauseAnimation();
    });

    this.ticker.addEventListener("mouseleave", () => {
      this.resumeAnimation();
    });

    // Setup mouse movement for orbs
    this.setupMouseEffects();
  }

  setupHoverEvents() {
    this.section.querySelectorAll(".ticker-item").forEach((item) => {
      item.addEventListener("mouseenter", () => {
        this.showPreview(item);
      });

      item.addEventListener("mouseleave", () => {
        this.hidePreview();
      });

      // Add click functionality
      item.addEventListener("click", () => {
        this.handleItemClick(item);
      });
    });
  }

  showPreview(item) {
    const imgPath = item.getAttribute("data-img");

    // Update preview image only
    this.previewImg.src = imgPath;

    // Position preview to emerge from the center of the text
    const rect = item.getBoundingClientRect();
    const previewWidth = 480; // Larger container to accommodate tilt
    const previewHeight = 360;

    // Start from the exact center of the ticker item text
    const textCenterX = rect.left + rect.width / 2;
    const textCenterY = rect.top + rect.height / 2;

    // Position preview centered on the text center
    let left = textCenterX - previewWidth / 2;
    let top = textCenterY - previewHeight / 2;

    // Adjust if preview goes off screen horizontally
    if (left < 20) left = 20;
    if (left + previewWidth > window.innerWidth - 20) {
      left = window.innerWidth - previewWidth - 20;
    }

    // Adjust if preview goes off screen vertically
    if (top < 20) top = 20;
    if (top + previewHeight > window.innerHeight - 20) {
      top = window.innerHeight - previewHeight - 20;
    }

    // Set initial position (will start small and grow from text center)
    this.preview.style.left = `${left}px`;
    this.preview.style.top = `${top}px`;

    // Set transform origin to the text center for scaling effect
    const originX = textCenterX - left;
    const originY = textCenterY - top;
    this.preview.style.transformOrigin = `${originX}px ${originY}px`;

    // Show preview with animation
    this.preview.classList.add("show");
    this.isHovered = true;
  }

  hidePreview() {
    if (!this.isHovered) return;

    this.preview.classList.remove("show");
    this.isHovered = false;
  }

  setupIndicators() {
    this.indicators.forEach((dot, index) => {
      dot.addEventListener("click", () => {
        this.setActiveIndicator(index);
      });
    });
  }

  setActiveIndicator(index) {
    this.indicators.forEach((dot) => dot.classList.remove("active"));
    this.indicators[index].classList.add("active");
    this.currentIndex = index;
  }

  startIndicatorAnimation() {
    setInterval(() => {
      if (!this.animationPaused) {
        this.currentIndex = (this.currentIndex + 1) % this.indicators.length;
        this.setActiveIndicator(this.currentIndex);
      }
    }, 6000); // Change indicator every 6 seconds
  }

  pauseAnimation() {
    this.animationPaused = true;
    this.ticker.style.animationPlayState = "paused";

    // Also pause progress bar
    const progressFill = this.section.querySelector(".progress-fill");
    if (progressFill) {
      progressFill.style.animationPlayState = "paused";
    }
  }

  resumeAnimation() {
    if (!this.isHovered) {
      this.animationPaused = false;
      this.ticker.style.animationPlayState = "running";

      // Resume progress bar
      const progressFill = this.section.querySelector(".progress-fill");
      if (progressFill) {
        progressFill.style.animationPlayState = "running";
      }
    }
  }

  setupMouseEffects() {
    // Add mouse movement effect for floating orbs
    this.section.addEventListener("mousemove", (e) => {
      const rect = this.section.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      const orbs = this.section.querySelectorAll(".ticker-floating-orb");
      orbs.forEach((orb, index) => {
        const speed = (index + 1) * 0.02;
        const moveX = x * speed;
        const moveY = y * speed;

        orb.style.transform = `translate(${moveX}px, ${moveY}px)`;
      });
    });
  }

  handleItemClick(item) {
    const title = item.querySelector(".item-title").textContent;
    const category = item.getAttribute("data-category");

    // Add click animation
    item.style.transform = "scale(0.95)";
    setTimeout(() => {
      item.style.transform = "";
    }, 150);

    // You can add more functionality here, like opening a modal or navigating
    console.log(`Clicked on: ${title} (${category})`);

    // Example: Show alert (replace with your desired functionality)
    this.showNotification(`Selected: ${title}`, category);
  }

  showNotification(message, category) {
    // Create notification element
    const notification = document.createElement("div");
    notification.className = "notification";
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-category">${category}</span>
        <span class="notification-message">${message}</span>
      </div>
    `;

    // Add notification styles
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(26, 26, 26, 0.95);
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 12px;
      border: 1px solid rgba(0, 255, 200, 0.3);
      backdrop-filter: blur(10px);
      z-index: 1000;
      transform: translateX(100%);
      transition: transform 0.3s ease;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = "translateX(0)";
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.transform = "translateX(100%)";
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }
}

// Hero Section JavaScript Functions
// handleCTAClick function is defined in the template to handle navigation

function initializeHeroSection() {
  const heroSection = document.querySelector(".hero-section");
  if (!heroSection) return;

  // Apply custom colors from data attributes
  const bgColor = heroSection.dataset.bgColor || "#000";
  const textColor = heroSection.dataset.textColor || "#fff";
  const accentColor = heroSection.dataset.accentColor || "#ffd700";

  // Set CSS custom properties for theming
  heroSection.style.setProperty("--hero-bg-color", bgColor);
  heroSection.style.setProperty("--hero-text-color", textColor);
  heroSection.style.setProperty("--hero-accent-color", accentColor);

  // Initialize background text animations
  initializeBackgroundText();

  // Initialize gallery animations
  initializeGalleryAnimations();

  // Initialize logo animations
  initializeLogoAnimations();
}

function initializeBackgroundText() {
  const bgText = document.querySelector(".hero-bg-text");
  if (!bgText) return;

  const animationSpeed = bgText.dataset.animationSpeed || "15s";
  const rows = bgText.querySelectorAll(".bg-text-row");

  rows.forEach((row, index) => {
    const direction =
      row.dataset.direction || (index % 2 === 0 ? "right" : "left");
    row.style.animationDuration = animationSpeed;
    row.style.animationName = direction === "right" ? "moveRight" : "moveLeft";
  });
}

function initializeGalleryAnimations() {
  const gallery = document.querySelector(".hero-gallery");
  if (!gallery) return;

  const animationSpeed = gallery.dataset.animationSpeed || "15s";
  const columns = gallery.querySelectorAll(".gallery-column");

  columns.forEach((column) => {
    const direction = column.dataset.direction || "up";
    column.style.animationDuration = animationSpeed;
    column.style.animationName = direction === "up" ? "scrollUp" : "scrollDown";
  });
}

function initializeLogoAnimations() {
  const logo = document.querySelector('.hero-logo[data-animate="true"]');
  if (!logo) {
    // If no data-animate attribute, just make sure any hero-logo is visible
    const anyLogo = document.querySelector(".hero-logo");
    if (anyLogo) {
      anyLogo.style.opacity = "1";
      anyLogo.style.transform = "scale(1) translateY(0)";
      anyLogo.style.clipPath = "inset(0 0 0 0)";
    }
    return;
  }

  // Logo animation sequence
  logo.style.opacity = "0";
  logo.style.transform = "scale(0.8) translateY(30px)";
  logo.style.clipPath = "inset(0 100% 0 0)";

  // Trigger animations
  setTimeout(() => {
    logo.style.animation = `
      wipeReveal 1s ease-out forwards,
      scaleRise 0.5s ease-out 1s forwards,
      logoGlow 3s ease-in-out 2.5s infinite
    `;
  }, 100);
}

// Modern Navbar Functionality
function initializeModernNavbar() {
  const navbar = document.querySelector(".modern-navbar");
  const navLinks = document.querySelectorAll(".nav-link");
  const mobileToggle = document.querySelector(".mobile-menu-toggle");

  if (!navbar) return;

  // Navbar scroll effect
  let lastScrollY = window.scrollY;

  function updateNavbar() {
    const currentScrollY = window.scrollY;

    if (currentScrollY > 50) {
      navbar.classList.add("scrolled");
    } else {
      navbar.classList.remove("scrolled");
    }

    lastScrollY = currentScrollY;
  }

  // Smooth scroll for navigation links
  navLinks.forEach((link) => {
    link.addEventListener("click", (e) => {
      const href = link.getAttribute("href");

      // Only handle hash links (internal page navigation)
      if (href && href.startsWith("#")) {
        e.preventDefault();
        const targetId = href.substring(1);
        const targetElement = document.getElementById(targetId);

        if (targetElement) {
          const navbarHeight = navbar.offsetHeight;
          const targetPosition = targetElement.offsetTop - navbarHeight - 20;

          window.scrollTo({
            top: targetPosition,
            behavior: "smooth",
          });

          // Update active link
          navLinks.forEach((l) => l.classList.remove("active"));
          link.classList.add("active");
        }
      }
    });
  });

  // Update active link based on scroll position
  function updateActiveLink() {
    const sections = document.querySelectorAll("section[id]");
    const navbarHeight = navbar.offsetHeight;

    sections.forEach((section) => {
      const sectionTop = section.offsetTop - navbarHeight - 100;
      const sectionBottom = sectionTop + section.offsetHeight;
      const currentScroll = window.scrollY;

      if (currentScroll >= sectionTop && currentScroll < sectionBottom) {
        const sectionId = section.getAttribute("id");
        const correspondingLink = document.querySelector(
          `.nav-link[href="#${sectionId}"]`
        );

        if (correspondingLink) {
          navLinks.forEach((l) => l.classList.remove("active"));
          correspondingLink.classList.add("active");
        }
      }
    });
  }

  // Event listeners
  window.addEventListener("scroll", () => {
    updateNavbar();
    updateActiveLink();
  });

  // Mobile menu toggle functionality
  if (mobileToggle) {
    const mobileMenu = document.getElementById("mobileNavMenu");

    console.log("Mobile toggle found:", mobileToggle);
    console.log("Mobile menu found:", mobileMenu);

    if (mobileMenu) {
      mobileToggle.addEventListener("click", (e) => {
        e.preventDefault();
        e.stopPropagation();

        console.log("Mobile menu toggle clicked");

        const isActive = mobileToggle.classList.contains("active");

        if (isActive) {
          // Close menu
          mobileToggle.classList.remove("active");
          mobileMenu.classList.remove("active");
          console.log("Menu closed");
        } else {
          // Open menu
          mobileToggle.classList.add("active");
          mobileMenu.classList.add("active");
          console.log("Menu opened");
        }
      });

      // Close menu when clicking on a link
      const mobileNavLinks = mobileMenu.querySelectorAll(".nav-link");
      mobileNavLinks.forEach((link) => {
        link.addEventListener("click", () => {
          mobileToggle.classList.remove("active");
          mobileMenu.classList.remove("active");
          console.log("Menu closed via link click");
        });
      });

      // Close menu when clicking outside
      document.addEventListener("click", (e) => {
        if (
          !mobileToggle.contains(e.target) &&
          !mobileMenu.contains(e.target)
        ) {
          mobileToggle.classList.remove("active");
          mobileMenu.classList.remove("active");
        }
      });

      // Close menu on escape key
      document.addEventListener("keydown", (e) => {
        if (e.key === "Escape") {
          mobileToggle.classList.remove("active");
          mobileMenu.classList.remove("active");
        }
      });
    } else {
      console.error("Mobile menu element not found!");
    }
  } else {
    console.error("Mobile toggle button not found!");
  }

  // Initial calls
  updateNavbar();
  updateActiveLink();
}

// Initialize ServicesTicker when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  // Initialize the services ticker when this section loads
  const section = document.getElementById("services-ticker");
  if (section && !section.dataset.initialized) {
    section.dataset.initialized = "true";
    new ServicesTicker();
  }

  // Initialize hero section
  initializeHeroSection();

  // Initialize modern navbar
  initializeModernNavbar();
});
