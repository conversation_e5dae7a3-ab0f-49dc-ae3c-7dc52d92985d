# Generated by Django 5.2.1 on 2025-06-19 17:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0003_aboutsection'),
    ]

    operations = [
        migrations.CreateModel(
            name='CaseStudy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='Case Study', help_text='Page title', max_length=200)),
                ('image_1', models.ImageField(blank=True, help_text='First curved square image', null=True, upload_to='casestudy/')),
                ('image_1_alt', models.Char<PERSON>ield(default='Case Study Image 1', help_text='Alt text for first image', max_length=200)),
                ('image_2', models.ImageField(blank=True, help_text='Second curved square image', null=True, upload_to='casestudy/')),
                ('image_2_alt', models.<PERSON><PERSON><PERSON><PERSON>(default='Case Study Image 2', help_text='Alt text for second image', max_length=200)),
                ('description', models.TextField(default='This is a beautiful paragraph text displayed in black color, perfectly centered below the curved square images. It demonstrates elegant typography and layout design.', help_text='Main description text')),
                ('hover_image', models.ImageField(blank=True, help_text='Center hover animation image', null=True, upload_to='casestudy/')),
                ('hover_image_alt', models.CharField(default='Hover Image', help_text='Alt text for hover image', max_length=200)),
                ('video_file', models.FileField(blank=True, help_text='Upload video file (MP4 recommended)', null=True, upload_to='casestudy/videos/')),
                ('video_url', models.URLField(blank=True, help_text='Or provide video URL')),
                ('video_poster', models.ImageField(blank=True, help_text='Video thumbnail/poster image', null=True, upload_to='casestudy/videos/posters/')),
                ('video_autoplay', models.BooleanField(default=False, help_text='Auto-play video')),
                ('video_muted', models.BooleanField(default=True, help_text='Start video muted')),
                ('video_loop', models.BooleanField(default=False, help_text='Loop video')),
                ('video_controls', models.BooleanField(default=True, help_text='Show video controls')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Case Study',
                'verbose_name_plural': 'Case Study',
            },
        ),
    ]
