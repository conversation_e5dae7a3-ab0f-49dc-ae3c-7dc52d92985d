{% extends 'base.html' %} {% load static %} {% block title %}Case Studies - {{
site_settings.site_title }}{% endblock %} {% block extra_css %}
<link rel="stylesheet" href="{% static 'css/casestudies.css' %}" />
<link rel="stylesheet" href="{% static 'css/casestudy-navbar.css' %}?v=1" />
{% endblock %} {% block content %}
<div class="casestudies-page case-study-page">
  <!-- Header Section -->
  <section class="casestudies-header" style="padding-top: 140px">
    <div class="header-content">
      <h1 class="page-title">Our Case Studies</h1>
      <p class="page-subtitle">
        Explore our portfolio of successful projects and see how we've helped
        clients achieve their goals through innovative digital solutions.
      </p>
    </div>
  </section>

  <!-- Case Studies Grid -->
  <section class="casestudies-grid">
    <div class="grid-container">
      {% for case_study in case_studies %}
      <div class="casestudy-card">
        <div class="card-images">
          {% if case_study.image_1 %}
          <img
            src="{{ case_study.image_1.url }}"
            alt="{{ case_study.image_1_alt }}"
            class="card-image primary"
          />
          {% else %}
          <img
            src="{% static 'images/img1.jpeg' %}"
            alt="Case Study Image"
            class="card-image primary"
          />
          {% endif %} {% if case_study.image_2 %}
          <img
            src="{{ case_study.image_2.url }}"
            alt="{{ case_study.image_2_alt }}"
            class="card-image secondary"
          />
          {% endif %}
        </div>

        <div class="card-content">
          <div class="card-meta">
            <span class="client-name">{{ case_study.client_name }}</span>
            <span class="project-category"
              >{{ case_study.project_category }}</span
            >
          </div>

          <h3 class="card-title">{{ case_study.title }}</h3>

          <p class="card-excerpt">{{ case_study.excerpt }}</p>

          {% if case_study.slug %}
          <a href="{{ case_study.get_absolute_url }}" class="card-link">
            View Case Study →
          </a>
          {% else %}
          <span class="card-link disabled"> Case Study (No URL) </span>
          {% endif %}
        </div>

        {% if case_study.is_featured %}
        <div class="featured-badge">Featured</div>
        {% endif %}
      </div>
      {% empty %}
      <div class="no-casestudies">
        <h3>No Case Studies Available</h3>
        <p>
          We're working on adding some amazing case studies. Check back soon!
        </p>
      </div>
      {% endfor %}
    </div>
  </section>

  <!-- CTA Button -->
  <section class="cta-button-section">
    <div class="button-container">
      <a href="{% url 'netfyre_app:contact' %}" class="cta-button"
        >Get Started</a
      >
    </div>
  </section>
</div>
{% endblock %}
