# Generated by Django 5.2.1 on 2025-06-21 05:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0012_privacypolicysection_privacypolicysettings'),
    ]

    operations = [
        migrations.CreateModel(
            name='CookieConsent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(help_text='Session identifier', max_length=40)),
                ('ip_address', models.GenericIPAddressField(help_text="User's IP address")),
                ('user_agent', models.TextField(blank=True, help_text='Browser user agent')),
                ('consent_given', models.BooleanField(default=False, help_text='Whether user accepted cookies')),
                ('consent_declined', models.BooleanField(default=False, help_text='Whether user declined cookies')),
                ('necessary_cookies', models.<PERSON><PERSON>an<PERSON>ield(default=True, help_text='Necessary cookies (always true)')),
                ('analytics_cookies', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Analytics cookies consent')),
                ('marketing_cookies', models.BooleanField(default=False, help_text='Marketing cookies consent')),
                ('preferences_cookies', models.BooleanField(default=False, help_text='Preferences cookies consent')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When consent was first given')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When consent was last updated')),
                ('page_url', models.URLField(blank=True, help_text='Page where consent was given')),
                ('referrer', models.URLField(blank=True, help_text='Referrer page')),
            ],
            options={
                'verbose_name': 'Cookie Consent',
                'verbose_name_plural': 'Cookie Consents',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['session_key'], name='netfyre_app_session_c00391_idx'), models.Index(fields=['ip_address'], name='netfyre_app_ip_addr_52dfe1_idx'), models.Index(fields=['created_at'], name='netfyre_app_created_cd260f_idx')],
            },
        ),
        migrations.CreateModel(
            name='CookieActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('banner_shown', 'Banner Shown'), ('accepted_all', 'Accepted All Cookies'), ('declined_all', 'Declined All Cookies'), ('settings_opened', 'Settings Modal Opened'), ('settings_saved', 'Custom Settings Saved'), ('consent_updated', 'Consent Updated')], help_text='Type of action performed', max_length=20)),
                ('timestamp', models.DateTimeField(auto_now_add=True, help_text='When the action occurred')),
                ('page_url', models.URLField(blank=True, help_text='Page where action occurred')),
                ('user_agent', models.TextField(blank=True, help_text='Browser user agent')),
                ('settings_snapshot', models.JSONField(blank=True, help_text='Cookie settings at time of action', null=True)),
                ('consent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to='netfyre_app.cookieconsent')),
            ],
            options={
                'verbose_name': 'Cookie Activity',
                'verbose_name_plural': 'Cookie Activities',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['action'], name='netfyre_app_action_476b7b_idx'), models.Index(fields=['timestamp'], name='netfyre_app_timesta_86fffe_idx')],
            },
        ),
    ]
