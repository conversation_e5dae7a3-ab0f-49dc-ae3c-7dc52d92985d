/* Case Studies List Page Styles */

/* Basic Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Arial", sans-serif;
  background-color: #ffffff;
  color: #000000;
  overflow-x: hidden;
}

/* Case Studies Page Container */
.casestudies-page {
  position: relative;
  z-index: 10;
  width: 1fr;
  min-height: 100vh;
  background: white;
}

/* Header Section */
.casestudies-header {
  padding: 100px 0 80px 0;
  background: linear-gradient(135deg, #ffffff 0%, #ffffff 100%);
  text-align: center;
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 10px;
  margin-top: -100px;
}

.page-title {
  font-size: 48px;
  font-weight: bold;
  color: #000;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.page-subtitle {
  font-size: 18px;
  color: #666;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Case Studies Grid */
.casestudies-grid {
  padding: 10px 0;
  background: rgb(255, 255, 255);
  
}

.grid-container {
  max-width: 1200px;
  height: fit-content;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

/* Case Study Card */
.casestudy-card {
  position: relative;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  height: fit-content;
}

.casestudy-card:hover {
  transform: translateY(-20px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

/* Card Images */
.card-images {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card-image.primary {
  position: relative;
  top: 0;
  left: 0;
  z-index: 2;
}

.card-image.secondary {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 80px;
  height: 80px;
  border-radius: 10px;
  z-index: 3;
  border: 3px solid white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.casestudy-card:hover .card-image.primary {
  transform: scale(1.05);
}

/* Card Content */
.card-content {
  padding: 30px;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.client-name {
  font-size: 14px;
  font-weight: bold;
  color: #000;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.project-category {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 12px;
}

.card-title {
  font-size: 24px;
  font-weight: bold;
  color: #000;
  margin-bottom: 15px;
  line-height: 1.3;
}

.card-excerpt {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.card-link {
  display: inline-flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #ffb600;
  text-decoration: none;
  transition: color 0.3s ease;
}

.card-link:hover {
  color: #333;
}

.card-link.disabled {
  color: #ccc;
  cursor: not-allowed;
}

/* Featured Badge */
.featured-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  background: linear-gradient(45deg, #000, #333);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  z-index: 4;
}

/* No Case Studies Message */
.no-casestudies {
  grid-column: 1 / -1;
  text-align: center;
  padding: 80px 20px;
}

.no-casestudies h3 {
  font-size: 32px;
  color: #000;
  margin-bottom: 15px;
}

.no-casestudies p {
  font-size: 18px;
  color: #666;
}

/* CTA Button Section */
.cta-button-section {
  text-align: center;
  padding: 60px 20px;
  background: #f8f9fa;
}

.button-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* CTA Button */
.cta-button {
  display: inline-block;
  background: rgb(0, 0, 0);
  color: #ffffff;
  padding: 15px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: bold;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.2);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: #ffb600;
  color: #000000;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-title {
    font-size: 32px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .grid-container {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 0 15px;
  }

  .card-content {
    padding: 20px;
  }

  .card-title {
    font-size: 20px;
  }
}
