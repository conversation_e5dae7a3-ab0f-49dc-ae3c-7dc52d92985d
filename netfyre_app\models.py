from django.db import models
from django.urls import reverse


class Service(models.Model):
    """Model for services offered by NetFyre"""
    title = models.CharField(max_length=100)
    subtitle = models.CharField(max_length=200)
    icon = models.CharField(max_length=50, help_text="Emoji or icon class")
    category = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to='services/', blank=True, null=True)
    order = models.PositiveIntegerField(default=0, help_text="Order of display")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'title']

    def __str__(self):
        return self.title


class Testimonial(models.Model):
    """Model for client testimonials"""
    client_name = models.CharField(max_length=100)
    client_title = models.CharField(max_length=200)
    client_company = models.CharField(max_length=100)
    client_image = models.ImageField(upload_to='testimonials/', blank=True, null=True)
    testimonial_text = models.TextField()
    rating = models.PositiveIntegerField(default=5, choices=[(i, i) for i in range(1, 6)])
    is_featured = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-is_featured', '-created_at']

    def __str__(self):
        return f"{self.client_name} - {self.client_company}"


class Project(models.Model):
    """Model for portfolio projects"""
    title = models.CharField(max_length=200)
    description = models.TextField()
    image = models.ImageField(upload_to='projects/')
    client = models.CharField(max_length=100, blank=True)
    project_url = models.URLField(blank=True)
    technologies = models.CharField(max_length=300, help_text="Comma-separated list of technologies")
    completion_date = models.DateField()
    is_featured = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-is_featured', 'order', '-completion_date']

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('project_detail', kwargs={'pk': self.pk})


class CompanyLogo(models.Model):
    """Model for trusted company logos"""
    company_name = models.CharField(max_length=100)
    logo_image = models.ImageField(upload_to='logos/')
    website_url = models.URLField(blank=True)
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', 'company_name']

    def __str__(self):
        return self.company_name


class FAQ(models.Model):
    """Model for frequently asked questions"""
    question = models.CharField(max_length=300)
    answer = models.TextField()
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'question']
        verbose_name = "FAQ"
        verbose_name_plural = "FAQs"

    def __str__(self):
        return self.question


class SiteSettings(models.Model):
    """Model for site-wide settings"""
    site_title = models.CharField(max_length=100, default="NetFyre")
    site_tagline = models.CharField(max_length=200, default="Digital Marketing Agency")
    hero_title = models.CharField(max_length=200, default="Increase Your Reach, Explode Your Sales.")
    hero_subtitle = models.TextField(default="Our tailored approach to brand success means better clicks, higher quality traffic, scroll-stopping ads, better ROAS and ultimately... more profit.")

    # Team Section Content
    team_title = models.CharField(max_length=200, default="Increase Your Reach, Explode Your Sales.", help_text="Main team section title")
    team_subtitle = models.TextField(default="Our tailored approach to brand success means better clicks, higher quality traffic, scroll-stopping ads, better ROAS and ultimately... more profit.", help_text="Team section description")
    storytellers_title = models.CharField(max_length=200, default="Meet the Storytellers of Your Success", help_text="Storytellers section title")
    storytellers_description = models.TextField(default="Behind every bold campaign and powerful strategy is a team of creative minds turning insights into impact. We're not just marketers—we're storytellers, strategists, designers, and data lovers dedicated to shaping your brand's narrative and driving real growth. Get to know the team that makes your success our mission.", help_text="Storytellers section description")

    contact_email = models.EmailField(default="<EMAIL>")
    phone_number = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    logo = models.ImageField(upload_to='site/', blank=True, null=True)
    favicon = models.ImageField(upload_to='site/', blank=True, null=True)
    
    # Social media links
    facebook_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)
    linkedin_url = models.URLField(blank=True)
    instagram_url = models.URLField(blank=True)
    
    # Analytics and tracking
    google_analytics_id = models.CharField(max_length=50, blank=True)
    facebook_pixel_id = models.CharField(max_length=50, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Site Settings"
        verbose_name_plural = "Site Settings"

    def __str__(self):
        return self.site_title

    def save(self, *args, **kwargs):
        # Ensure only one instance exists
        if not self.pk and SiteSettings.objects.exists():
            raise ValueError('Only one SiteSettings instance is allowed')
        return super().save(*args, **kwargs)


class Counter(models.Model):
    """Model for counter statistics"""
    label = models.CharField(max_length=100)
    value = models.PositiveIntegerField()
    prefix = models.CharField(max_length=10, blank=True, help_text="e.g., $, #")
    suffix = models.CharField(max_length=10, blank=True, help_text="e.g., +, %")
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'label']

    def __str__(self):
        return f"{self.prefix}{self.value}{self.suffix} - {self.label}"


class GalleryImage(models.Model):
    """Model for hero section gallery images"""
    COLUMN_CHOICES = [
        (1, 'Column 1 (Scrolling Up)'),
        (2, 'Column 2 (Scrolling Down)'),
        (3, 'Column 3 (Scrolling Up)'),
    ]

    title = models.CharField(max_length=200, help_text="Image title/description")
    image = models.ImageField(upload_to='gallery/', help_text="Upload gallery image")
    column = models.PositiveIntegerField(choices=COLUMN_CHOICES, default=1, help_text="Which column to display in")
    order = models.PositiveIntegerField(default=0, help_text="Order within the column")
    alt_text = models.CharField(max_length=200, blank=True, help_text="Alt text for accessibility")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['column', 'order', 'title']
        verbose_name = "Gallery Image"
        verbose_name_plural = "Gallery Images"

    def __str__(self):
        return f"{self.title} (Column {self.column})"

    def save(self, *args, **kwargs):
        # Auto-generate alt text if not provided
        if not self.alt_text:
            self.alt_text = self.title
        super().save(*args, **kwargs)


class AboutSection(models.Model):
    """Model for the About/Strategy section"""
    title = models.CharField(max_length=200, default="Our Strategy", help_text="Main section title")
    subtitle = models.CharField(max_length=300, default="How We Scale Brands To 7-Figure Months", help_text="Section subtitle")
    video_file = models.FileField(upload_to='videos/', blank=True, null=True, help_text="Upload video file (MP4 recommended)")
    video_url = models.URLField(blank=True, help_text="Or provide video URL (YouTube, Vimeo, etc.)")
    video_poster = models.ImageField(upload_to='videos/posters/', blank=True, null=True, help_text="Video thumbnail/poster image")

    # Video settings
    autoplay = models.BooleanField(default=True, help_text="Auto-play video")
    muted = models.BooleanField(default=True, help_text="Start video muted")
    loop = models.BooleanField(default=True, help_text="Loop video")
    controls = models.BooleanField(default=True, help_text="Show video controls")

    # Additional content
    description = models.TextField(blank=True, help_text="Optional description text below the video")

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "About Section"
        verbose_name_plural = "About Section"

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        # Ensure only one instance exists
        if not self.pk and AboutSection.objects.exists():
            raise ValueError('Only one AboutSection instance is allowed')
        return super().save(*args, **kwargs)

    def get_video_source(self):
        """Return the video source - either file or URL"""
        if self.video_file:
            return self.video_file.url
        elif self.video_url:
            return self.video_url
        else:
            return "https://www.w3schools.com/html/mov_bbb.mp4"  # Default fallback

    def get_youtube_embed_url(self):
        """Extract YouTube video ID and return embed URL"""
        if not self.video_url:
            return None

        import re
        # Handle different YouTube URL formats including Shorts
        youtube_patterns = [
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]+)',
            r'(?:https?://)?(?:www\.)?youtube\.com/embed/([a-zA-Z0-9_-]+)',
            r'(?:https?://)?(?:www\.)?youtube\.com/shorts/([a-zA-Z0-9_-]+)',
            r'(?:https?://)?youtu\.be/([a-zA-Z0-9_-]+)',
        ]

        for pattern in youtube_patterns:
            match = re.search(pattern, self.video_url)
            if match:
                video_id = match.group(1)
                # Remove any additional parameters from video_id
                video_id = video_id.split('&')[0].split('?')[0]
                return f"https://www.youtube.com/embed/{video_id}"

        return None

    def is_youtube_url(self):
        """Check if the video URL is a YouTube URL"""
        if not self.video_url:
            return False
        return 'youtube.com' in self.video_url or 'youtu.be' in self.video_url


class CaseStudy(models.Model):
    """Model for individual Case Studies"""
    title = models.CharField(max_length=200, default="Case Study", help_text="Case study title")
    slug = models.SlugField(max_length=200, unique=True, blank=True, help_text="URL slug for this case study")
    client_name = models.CharField(max_length=100, default="Default Client", help_text="Client/company name")
    project_category = models.CharField(max_length=100, default="Web Design", help_text="Project category (e.g., Web Design, Branding, etc.)")

    # Summary/excerpt for listing page
    excerpt = models.TextField(max_length=300, default="Default excerpt for this case study.", help_text="Short description for case study listing")

    # Top section images
    image_1 = models.ImageField(upload_to='casestudy/', blank=True, null=True, help_text="First curved square image")
    image_1_alt = models.CharField(max_length=200, default="Case Study Image 1", help_text="Alt text for first image")
    image_2 = models.ImageField(upload_to='casestudy/', blank=True, null=True, help_text="Second curved square image")
    image_2_alt = models.CharField(max_length=200, default="Case Study Image 2", help_text="Alt text for second image")

    # Overlay logo for second curved square image
    overlay_logo = models.ImageField(upload_to='casestudy/logos/', blank=True, null=True, help_text="Logo overlay for second curved square image (appears on hover)")
    overlay_logo_alt = models.CharField(max_length=200, default="Overlay Logo", help_text="Alt text for overlay logo")

    # Center description text
    description = models.TextField(help_text="Main description text")

    # Hover image
    hover_image = models.ImageField(upload_to='casestudy/', blank=True, null=True, help_text="Center hover animation image")
    hover_image_alt = models.CharField(max_length=200, default="Hover Image", help_text="Alt text for hover image")

    # Video section
    video_file = models.FileField(upload_to='casestudy/videos/', blank=True, null=True, help_text="Upload video file (MP4 recommended)")
    video_url = models.URLField(blank=True, help_text="Or provide video URL")
    video_poster = models.ImageField(upload_to='casestudy/videos/posters/', blank=True, null=True, help_text="Video thumbnail/poster image")

    # Video settings
    video_autoplay = models.BooleanField(default=False, help_text="Auto-play video")
    video_muted = models.BooleanField(default=True, help_text="Start video muted")
    video_loop = models.BooleanField(default=False, help_text="Loop video")
    video_controls = models.BooleanField(default=True, help_text="Show video controls")

    # Display settings
    is_featured = models.BooleanField(default=False, help_text="Feature this case study")
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0, help_text="Order for display")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Case Study"
        verbose_name_plural = "Case Studies"
        ordering = ['-is_featured', 'order', '-created_at']

    def __str__(self):
        return f"{self.title} - {self.client_name}"

    def get_absolute_url(self):
        return reverse('netfyre_app:casestudy_detail', kwargs={'slug': self.slug})

    def is_youtube_url(self):
        """Check if the video URL is a YouTube URL"""
        if not self.video_url:
            return False
        return 'youtube.com' in self.video_url or 'youtu.be' in self.video_url

    def get_youtube_embed_url(self):
        """Extract YouTube video ID and return embed URL"""
        if not self.video_url:
            return None

        import re
        # Handle different YouTube URL formats including Shorts
        youtube_patterns = [
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]+)',
            r'(?:https?://)?(?:www\.)?youtube\.com/embed/([a-zA-Z0-9_-]+)',
            r'(?:https?://)?(?:www\.)?youtube\.com/shorts/([a-zA-Z0-9_-]+)',
            r'(?:https?://)?youtu\.be/([a-zA-Z0-9_-]+)',
        ]

        for pattern in youtube_patterns:
            match = re.search(pattern, self.video_url)
            if match:
                video_id = match.group(1)
                # Remove any additional parameters from video_id
                video_id = video_id.split('&')[0].split('?')[0]
                return f"https://www.youtube.com/embed/{video_id}"

        return None

    def save(self, *args, **kwargs):
        if not self.slug:
            from django.utils.text import slugify
            base_slug = slugify(self.title)
            if not base_slug:  # If title is empty or only special characters
                base_slug = f"case-study-{self.pk or 'new'}"

            # Ensure slug is unique
            slug = base_slug
            counter = 1
            while CaseStudy.objects.filter(slug=slug).exclude(pk=self.pk).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1

            self.slug = slug
        super().save(*args, **kwargs)


class InfiniteScrollImage(models.Model):
    """Model for infinite scroll images in case study page"""
    title = models.CharField(max_length=200, help_text="Image title/description")
    image = models.ImageField(upload_to='infinite_scroll/', help_text="Upload image for infinite scroll")
    alt_text = models.CharField(max_length=200, blank=True, help_text="Alt text for accessibility")
    order = models.PositiveIntegerField(default=0, help_text="Order of display in the scroll")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'title']
        verbose_name = "Infinite Scroll Image"
        verbose_name_plural = "Infinite Scroll Images"

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        # Auto-generate alt text if not provided
        if not self.alt_text:
            self.alt_text = self.title
        super().save(*args, **kwargs)


class TrustedCompany(models.Model):
    """Model for trusted company logos in infinite scroll"""
    company_name = models.CharField(max_length=100, help_text="Company name")
    logo_image = models.ImageField(upload_to='trusted_companies/', blank=True, null=True, help_text="Company logo image (optional)")
    website_url = models.URLField(blank=True, help_text="Company website URL")
    order = models.PositiveIntegerField(default=0, help_text="Order of display in the scroll")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'company_name']
        verbose_name = "Trusted Company"
        verbose_name_plural = "Trusted Companies"

    def __str__(self):
        return self.company_name


class TeamMember(models.Model):
    """Model for team members"""
    ROLE_CHOICES = [
        ('founder', 'Founder'),
        ('ceo', 'CEO'),
        ('cto', 'CTO'),
        ('designer', 'Designer'),
        ('developer', 'Developer'),
        ('marketer', 'Digital Marketer'),
        ('strategist', 'Strategist'),
        ('manager', 'Project Manager'),
        ('analyst', 'Data Analyst'),
        ('copywriter', 'Copywriter'),
        ('videographer', 'Videographer'),
        ('other', 'Other'),
    ]

    name = models.CharField(max_length=100, help_text="Full name")
    role = models.CharField(max_length=50, choices=ROLE_CHOICES, help_text="Role/Position")
    custom_role = models.CharField(max_length=100, blank=True, help_text="Custom role if 'Other' is selected")
    bio = models.TextField(help_text="Professional biography")
    short_bio = models.CharField(max_length=200, help_text="Short bio for cards/previews")
    profile_image = models.ImageField(upload_to='team/', help_text="Professional headshot")
    cover_image = models.ImageField(upload_to='team/covers/', blank=True, null=True, help_text="Cover/background image")

    # Contact & Social
    email = models.EmailField(blank=True, help_text="Professional email")
    phone = models.CharField(max_length=20, blank=True, help_text="Phone number")
    linkedin_url = models.URLField(blank=True, help_text="LinkedIn profile")
    twitter_url = models.URLField(blank=True, help_text="Twitter profile")
    instagram_url = models.URLField(blank=True, help_text="Instagram profile")
    behance_url = models.URLField(blank=True, help_text="Behance portfolio")
    dribbble_url = models.URLField(blank=True, help_text="Dribbble portfolio")
    github_url = models.URLField(blank=True, help_text="GitHub profile")
    personal_website = models.URLField(blank=True, help_text="Personal website")

    # Professional Details
    years_experience = models.PositiveIntegerField(default=0, help_text="Years of experience")
    specialties = models.CharField(max_length=300, help_text="Comma-separated specialties/skills")
    achievements = models.TextField(blank=True, help_text="Key achievements and awards")
    education = models.CharField(max_length=200, blank=True, help_text="Educational background")

    # Display Settings
    order = models.PositiveIntegerField(default=0, help_text="Display order")
    is_featured = models.BooleanField(default=False, help_text="Show on homepage")
    is_active = models.BooleanField(default=True, help_text="Active team member")
    show_contact = models.BooleanField(default=True, help_text="Show contact information")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'name']
        verbose_name = "Team Member"
        verbose_name_plural = "Team Members"

    def __str__(self):
        return f"{self.name} - {self.get_display_role()}"

    def get_display_role(self):
        """Get the display role (custom role if specified, otherwise role choice)"""
        if self.role == 'other' and self.custom_role:
            return self.custom_role
        return self.get_role_display()

    def get_specialties_list(self):
        """Get specialties as a list"""
        return [s.strip() for s in self.specialties.split(',') if s.strip()]


class TeamMemberPortfolioImage(models.Model):
    """Model for team member portfolio images"""
    team_member = models.ForeignKey(TeamMember, on_delete=models.CASCADE, related_name='portfolio_images')
    title = models.CharField(max_length=200, help_text="Image title/caption")
    image = models.ImageField(upload_to='team/portfolio/', help_text="Portfolio image")
    description = models.TextField(blank=True, help_text="Image description")
    category = models.CharField(max_length=100, blank=True, help_text="Image category (e.g., 'Web Design', 'Branding')")
    project_url = models.URLField(blank=True, help_text="Link to live project")
    order = models.PositiveIntegerField(default=0, help_text="Display order")
    is_featured = models.BooleanField(default=False, help_text="Featured in portfolio")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', '-created_at']
        verbose_name = "Portfolio Image"
        verbose_name_plural = "Portfolio Images"

    def __str__(self):
        return f"{self.team_member.name} - {self.title}"


class TeamMemberPortfolioVideo(models.Model):
    """Model for team member portfolio videos"""
    team_member = models.ForeignKey(TeamMember, on_delete=models.CASCADE, related_name='portfolio_videos')
    title = models.CharField(max_length=200, help_text="Video title")
    video_file = models.FileField(upload_to='team/videos/', blank=True, null=True, help_text="Upload video file")
    video_url = models.URLField(blank=True, help_text="Video URL (YouTube, Vimeo, etc.)")
    thumbnail = models.ImageField(upload_to='team/video_thumbnails/', blank=True, null=True, help_text="Video thumbnail")
    description = models.TextField(blank=True, help_text="Video description")
    category = models.CharField(max_length=100, blank=True, help_text="Video category")
    duration = models.CharField(max_length=20, blank=True, help_text="Video duration (e.g., '2:30')")
    order = models.PositiveIntegerField(default=0, help_text="Display order")
    is_featured = models.BooleanField(default=False, help_text="Featured in portfolio")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', '-created_at']
        verbose_name = "Portfolio Video"
        verbose_name_plural = "Portfolio Videos"

    def __str__(self):
        return f"{self.team_member.name} - {self.title}"

    def is_youtube_url(self):
        """Check if the video URL is a YouTube URL"""
        return 'youtube.com' in self.video_url or 'youtu.be' in self.video_url

    def get_youtube_embed_url(self):
        """Convert YouTube URL to embed URL"""
        import re
        # Handle different YouTube URL formats including Shorts
        youtube_patterns = [
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]+)',
            r'(?:https?://)?(?:www\.)?youtube\.com/embed/([a-zA-Z0-9_-]+)',
            r'(?:https?://)?(?:www\.)?youtube\.com/shorts/([a-zA-Z0-9_-]+)',
            r'(?:https?://)?youtu\.be/([a-zA-Z0-9_-]+)',
        ]

        for pattern in youtube_patterns:
            match = re.search(pattern, self.video_url)
            if match:
                video_id = match.group(1)
                # Remove any additional parameters from video_id
                video_id = video_id.split('&')[0].split('?')[0]
                return f"https://www.youtube.com/embed/{video_id}"

        return self.video_url


class TeamMemberProject(models.Model):
    """Model for team member individual projects"""
    team_member = models.ForeignKey(TeamMember, on_delete=models.CASCADE, related_name='projects')
    title = models.CharField(max_length=200, help_text="Project title")
    description = models.TextField(help_text="Project description")
    client = models.CharField(max_length=100, blank=True, help_text="Client name")
    role = models.CharField(max_length=100, help_text="Your role in this project")
    technologies = models.CharField(max_length=300, blank=True, help_text="Technologies used")
    project_url = models.URLField(blank=True, help_text="Live project URL")
    github_url = models.URLField(blank=True, help_text="GitHub repository URL")
    featured_image = models.ImageField(upload_to='team/projects/', help_text="Main project image")
    completion_date = models.DateField(help_text="Project completion date")
    order = models.PositiveIntegerField(default=0, help_text="Display order")
    is_featured = models.BooleanField(default=False, help_text="Featured project")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', '-completion_date']
        verbose_name = "Team Member Project"
        verbose_name_plural = "Team Member Projects"

    def __str__(self):
        return f"{self.team_member.name} - {self.title}"

    def get_technologies_list(self):
        """Get technologies as a list"""
        return [t.strip() for t in self.technologies.split(',') if t.strip()]


class ContactSubmission(models.Model):
    """Model for contact form submissions"""
    fullname = models.CharField(max_length=100, help_text="Full name of the contact")
    email = models.EmailField(help_text="Email address")
    phone = models.CharField(max_length=20, help_text="Phone number")
    website = models.URLField(help_text="Website URL")
    revenue = models.CharField(max_length=20, choices=[
        ('0-10k', '$0 - $10,000'),
        ('10k-50k', '$10,000 - $50,000'),
        ('50k-100k', '$50,000 - $100,000'),
        ('100k+', '$100,000+'),
    ], help_text="Monthly revenue range")
    source = models.CharField(max_length=20, choices=[
        ('google', 'Google Search'),
        ('social', 'Social Media'),
        ('referral', 'Referral'),
        ('other', 'Other'),
    ], help_text="How they heard about us")
    business_type = models.CharField(max_length=30, choices=[
        ('ecommerce', 'E-commerce'),
        ('saas', 'SaaS/Software'),
        ('healthcare', 'Healthcare'),
        ('finance', 'Finance/Banking'),
        ('education', 'Education'),
        ('real-estate', 'Real Estate'),
        ('restaurant', 'Restaurant/Food'),
        ('fitness', 'Fitness/Wellness'),
        ('consulting', 'Consulting'),
        ('manufacturing', 'Manufacturing'),
        ('nonprofit', 'Non-profit'),
        ('other', 'Other'),
    ], default='other', help_text="Type of business")
    submitted_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False, help_text="Mark as read")

    class Meta:
        ordering = ['-submitted_at']
        verbose_name = "Contact Submission"
        verbose_name_plural = "Contact Submissions"

    def __str__(self):
        return f"{self.fullname} - {self.email}"


class PrivacyPolicySection(models.Model):
    """Model for Privacy Policy sections"""
    title = models.CharField(max_length=200, help_text="Section title")
    content = models.TextField(help_text="Section content (supports HTML)")
    order = models.PositiveIntegerField(default=0, help_text="Display order")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'created_at']
        verbose_name = 'Privacy Policy Section'
        verbose_name_plural = 'Privacy Policy Sections'

    def __str__(self):
        return self.title


class PrivacyPolicySettings(models.Model):
    """Model for Privacy Policy page settings"""
    page_title = models.CharField(max_length=200, default="Privacy Policy", help_text="Main page title")
    subtitle = models.TextField(
        default="Your privacy is important to us. This privacy policy explains how we collect, use, and protect your information when you use our services.",
        help_text="Page subtitle/description"
    )
    contact_title = models.CharField(max_length=200, default="Contact Us", help_text="Contact section title")
    contact_description = models.TextField(
        default="If you have any questions about this Privacy Policy, please contact us:",
        help_text="Contact section description"
    )
    contact_email = models.EmailField(default="<EMAIL>", help_text="Privacy contact email")
    contact_phone = models.CharField(max_length=20, default="+****************", help_text="Privacy contact phone")
    contact_address = models.TextField(default="123 Business Street, City, State 12345", help_text="Privacy contact address")
    last_updated = models.DateTimeField(auto_now=True, help_text="Last updated timestamp")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Privacy Policy Settings'
        verbose_name_plural = 'Privacy Policy Settings'

    def __str__(self):
        return f"Privacy Policy Settings - {self.page_title}"

    def save(self, *args, **kwargs):
        # Ensure only one instance exists
        if not self.pk and PrivacyPolicySettings.objects.exists():
            raise ValueError('Only one Privacy Policy Settings instance is allowed')
        super().save(*args, **kwargs)


class CookieConsent(models.Model):
    """Model to track user cookie consent choices"""

    # User identification (can be anonymous)
    session_key = models.CharField(max_length=40, help_text="Session identifier")
    ip_address = models.GenericIPAddressField(help_text="User's IP address")
    user_agent = models.TextField(blank=True, help_text="Browser user agent")

    # Consent choices
    consent_given = models.BooleanField(default=False, help_text="Whether user accepted cookies")
    consent_declined = models.BooleanField(default=False, help_text="Whether user declined cookies")

    # Cookie categories
    necessary_cookies = models.BooleanField(default=True, help_text="Necessary cookies (always true)")
    analytics_cookies = models.BooleanField(default=False, help_text="Analytics cookies consent")
    marketing_cookies = models.BooleanField(default=False, help_text="Marketing cookies consent")
    preferences_cookies = models.BooleanField(default=False, help_text="Preferences cookies consent")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, help_text="When consent was first given")
    updated_at = models.DateTimeField(auto_now=True, help_text="When consent was last updated")

    # Additional data
    page_url = models.URLField(blank=True, help_text="Page where consent was given")
    referrer = models.URLField(blank=True, help_text="Referrer page")

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Cookie Consent'
        verbose_name_plural = 'Cookie Consents'
        indexes = [
            models.Index(fields=['session_key']),
            models.Index(fields=['ip_address']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        status = "Accepted" if self.consent_given else "Declined" if self.consent_declined else "Pending"
        return f"{self.ip_address} - {status} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"

    @property
    def consent_status(self):
        if self.consent_given:
            return "Accepted"
        elif self.consent_declined:
            return "Declined"
        else:
            return "Pending"

    @property
    def enabled_categories(self):
        categories = []
        if self.necessary_cookies:
            categories.append("Necessary")
        if self.analytics_cookies:
            categories.append("Analytics")
        if self.marketing_cookies:
            categories.append("Marketing")
        if self.preferences_cookies:
            categories.append("Preferences")
        return ", ".join(categories)


class CookieActivity(models.Model):
    """Model to track cookie-related activities and changes"""

    ACTION_CHOICES = [
        ('banner_shown', 'Banner Shown'),
        ('accepted_all', 'Accepted All Cookies'),
        ('declined_all', 'Declined All Cookies'),
        ('settings_opened', 'Settings Modal Opened'),
        ('settings_saved', 'Custom Settings Saved'),
        ('consent_updated', 'Consent Updated'),
    ]

    # Related consent record
    consent = models.ForeignKey(CookieConsent, on_delete=models.CASCADE, related_name='activities')

    # Activity details
    action = models.CharField(max_length=20, choices=ACTION_CHOICES, help_text="Type of action performed")
    timestamp = models.DateTimeField(auto_now_add=True, help_text="When the action occurred")

    # Context data
    page_url = models.URLField(blank=True, help_text="Page where action occurred")
    user_agent = models.TextField(blank=True, help_text="Browser user agent")

    # Settings snapshot (JSON field for flexibility)
    settings_snapshot = models.JSONField(blank=True, null=True, help_text="Cookie settings at time of action")

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'Cookie Activity'
        verbose_name_plural = 'Cookie Activities'
        indexes = [
            models.Index(fields=['action']),
            models.Index(fields=['timestamp']),
        ]

    def __str__(self):
        return f"{self.consent.ip_address} - {self.get_action_display()} ({self.timestamp.strftime('%Y-%m-%d %H:%M')})"


class TermsAndConditionsSection(models.Model):
    """Model for Terms and Conditions sections"""
    title = models.CharField(max_length=200, help_text="Section title")
    content = models.TextField(help_text="Section content (supports HTML)")
    order = models.PositiveIntegerField(default=0, help_text="Display order")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'created_at']
        verbose_name = 'Terms and Conditions Section'
        verbose_name_plural = 'Terms and Conditions Sections'

    def __str__(self):
        return self.title


class TermsAndConditionsSettings(models.Model):
    """Model for Terms and Conditions page settings"""
    page_title = models.CharField(max_length=200, default="Terms and Conditions", help_text="Main page title")
    subtitle = models.TextField(
        default="Please read these terms and conditions carefully before using our services. By accessing or using our website, you agree to be bound by these terms.",
        help_text="Page subtitle/description"
    )
    effective_date = models.DateField(help_text="When these terms became effective")
    contact_title = models.CharField(max_length=200, default="Contact Us", help_text="Contact section title")
    contact_description = models.TextField(
        default="If you have any questions about these Terms and Conditions, please contact us:",
        help_text="Contact section description"
    )
    contact_email = models.EmailField(default="<EMAIL>", help_text="Legal contact email")
    contact_phone = models.CharField(max_length=20, default="+****************", help_text="Legal contact phone")
    contact_address = models.TextField(default="123 Business Street, City, State 12345", help_text="Legal contact address")
    last_updated = models.DateTimeField(auto_now=True, help_text="Last updated timestamp")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Terms and Conditions Settings'
        verbose_name_plural = 'Terms and Conditions Settings'

    def __str__(self):
        return f"Terms and Conditions Settings - {self.page_title}"

    def save(self, *args, **kwargs):
        # Ensure only one instance exists
        if not self.pk and TermsAndConditionsSettings.objects.exists():
            raise ValueError('Only one Terms and Conditions Settings instance is allowed')
        super().save(*args, **kwargs)
