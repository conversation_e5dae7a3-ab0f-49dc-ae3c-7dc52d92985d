# Generated by Django 5.2.1 on 2025-06-19 10:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0002_galleryimage'),
    ]

    operations = [
        migrations.CreateModel(
            name='AboutSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='Our Strategy', help_text='Main section title', max_length=200)),
                ('subtitle', models.Char<PERSON>ield(default='How We Scale Brands To 7-Figure Months', help_text='Section subtitle', max_length=300)),
                ('video_file', models.FileField(blank=True, help_text='Upload video file (MP4 recommended)', null=True, upload_to='videos/')),
                ('video_url', models.URLField(blank=True, help_text='Or provide video URL (YouTube, Vimeo, etc.)')),
                ('video_poster', models.<PERSON>Field(blank=True, help_text='Video thumbnail/poster image', null=True, upload_to='videos/posters/')),
                ('autoplay', models.<PERSON><PERSON>anField(default=True, help_text='Auto-play video')),
                ('muted', models.BooleanField(default=True, help_text='Start video muted')),
                ('loop', models.BooleanField(default=True, help_text='Loop video')),
                ('controls', models.BooleanField(default=True, help_text='Show video controls')),
                ('description', models.TextField(blank=True, help_text='Optional description text below the video')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'About Section',
                'verbose_name_plural': 'About Section',
            },
        ),
    ]
