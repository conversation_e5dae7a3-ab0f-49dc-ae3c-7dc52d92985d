# Generated by Django 5.2.1 on 2025-06-21 06:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0014_termsandconditionssection_termsandconditionssettings'),
    ]

    operations = [
        migrations.CreateModel(
            name='NavbarSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('logo', models.ImageField(blank=True, help_text='Navbar logo image', null=True, upload_to='navbar/')),
                ('logo_alt_text', models.CharField(default='NetFyre', help_text='Logo alt text', max_length=100)),
                ('logo_width', models.PositiveIntegerField(default=120, help_text='Logo width in pixels')),
                ('logo_height', models.PositiveIntegerField(default=40, help_text='Logo height in pixels')),
                ('brand_text', models.CharField(default='NetFyre', help_text='Brand text if no logo', max_length=50)),
                ('brand_link', models.URLField(default='/', help_text='Where logo/brand links to')),
                ('navbar_style', models.CharField(choices=[('transparent', 'Transparent'), ('dark', 'Dark'), ('light', 'Light'), ('blur', 'Blur Effect')], default='blur', help_text='Navbar background style', max_length=20)),
                ('mobile_menu_enabled', models.BooleanField(default=True, help_text='Enable mobile hamburger menu')),
                ('cta_button_enabled', models.BooleanField(default=True, help_text='Show CTA button in navbar')),
                ('cta_button_text', models.CharField(default='Get Started', help_text='CTA button text', max_length=30)),
                ('cta_button_link', models.URLField(default='/contact/', help_text='CTA button link')),
                ('cta_button_style', models.CharField(choices=[('primary', 'Primary (Yellow)'), ('secondary', 'Secondary (White)'), ('outline', 'Outline')], default='primary', help_text='CTA button style', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Navbar Settings',
                'verbose_name_plural': 'Navbar Settings',
            },
        ),
        migrations.CreateModel(
            name='NavbarSocialLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform', models.CharField(choices=[('facebook', 'Facebook'), ('twitter', 'Twitter'), ('instagram', 'Instagram'), ('linkedin', 'LinkedIn'), ('youtube', 'YouTube'), ('github', 'GitHub'), ('dribbble', 'Dribbble'), ('behance', 'Behance')], help_text='Social media platform', max_length=20)),
                ('url', models.URLField(help_text='Social media profile URL')),
                ('icon_class', models.CharField(blank=True, help_text='Custom icon class (leave blank for default)', max_length=50)),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('is_active', models.BooleanField(default=True)),
                ('show_in_navbar', models.BooleanField(default=False, help_text='Show in navbar (usually only for mobile)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Navbar Social Link',
                'verbose_name_plural': 'Navbar Social Links',
                'ordering': ['order', 'platform'],
            },
        ),
        migrations.CreateModel(
            name='NavbarLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Link text', max_length=50)),
                ('url', models.CharField(help_text="Link URL (can be relative like '/about/' or absolute)", max_length=200)),
                ('link_type', models.CharField(choices=[('internal', 'Internal Page'), ('external', 'External URL'), ('dropdown', 'Dropdown Menu')], default='internal', help_text='Type of link', max_length=20)),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('is_active', models.BooleanField(default=True)),
                ('open_in_new_tab', models.BooleanField(default=False, help_text='Open link in new tab')),
                ('icon_class', models.CharField(blank=True, help_text='CSS icon class (optional)', max_length=50)),
                ('css_class', models.CharField(blank=True, help_text='Additional CSS classes', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent_link', models.ForeignKey(blank=True, help_text='Parent link for dropdown items', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='dropdown_items', to='netfyre_app.navbarlink')),
            ],
            options={
                'verbose_name': 'Navbar Link',
                'verbose_name_plural': 'Navbar Links',
                'ordering': ['order', 'title'],
            },
        ),
    ]
