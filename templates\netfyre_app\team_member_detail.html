{% extends 'base.html' %} {% load static %} {% block title %} {{
team_member.name }} - {{ team_member.get_display_role }} - {{
site_settings.site_title }} {% endblock %} {% block extra_css %}
<link rel="stylesheet" href="{% static 'css/casestudy.css' %}" />
{% endblock %} {% block content %}

<!-- Team Member Hero Section -->
<section class="member-hero">
  {% if team_member.cover_image %}
  <div
    class="hero-background"
    style="background-image: url('{{ team_member.cover_image.url }}');"
  ></div>
  {% else %}
  <div
    class="hero-background"
    style="background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)"
  ></div>
  {% endif %}
  <div class="hero-overlay"></div>

  <div class="hero-content">
    <div class="member-profile">
      {% if team_member.profile_image %}
      <img
        src="{{ team_member.profile_image.url }}"
        alt="{{ team_member.name }}"
        class="profile-image"
      />
      {% else %}
      <img
        src="{% static 'images/default-avatar.png' %}"
        alt="{{ team_member.name }}"
        class="profile-image"
      />
      {% endif %}

      <div class="profile-info">
        <h1>{{ team_member.name }}</h1>
        <p class="role">{{ team_member.get_display_role }}</p>
        <p class="experience">
          {{ team_member.years_experience }} years of experience
        </p>
        <p class="bio">{{ team_member.bio }}</p>

        {% if team_member.show_contact %}
        <div class="contact-links">
          {% if team_member.email %}<a
            href="mailto:{{ team_member.email }}"
            class="contact-btn"
            >Email</a
          >{% endif %} {% if team_member.linkedin_url %}<a
            href="{{ team_member.linkedin_url }}"
            target="_blank"
            class="contact-btn"
            >LinkedIn</a
          >{% endif %} {% if team_member.personal_website %}<a
            href="{{ team_member.personal_website }}"
            target="_blank"
            class="contact-btn"
            >Website</a
          >{% endif %}
        </div>
        {% endif %}
      </div>
    </div>

    <div class="portfolio-overview">
      <div class="overview-stat">
        <div class="stat-number">{{ portfolio_images.count }}</div>
        <div class="stat-label">Portfolio Images</div>
      </div>
      <div class="overview-stat">
        <div class="stat-number">{{ portfolio_videos.count }}</div>
        <div class="stat-label">Videos</div>
      </div>
      <div class="overview-stat">
        <div class="stat-number">{{ projects.count }}</div>
        <div class="stat-label">Projects</div>
      </div>
    </div>
  </div>
</section>

<section class="top-images">
  <div class="container">
    <div class="curved-images">
      {% if portfolio_images %} {% for image in portfolio_images|slice:":2" %}
      <div class="curved-square-wrapper">
        <img
          src="{{ image.image.url }}"
          alt="{{ image.title }}"
          class="curved-square"
        />
        <div class="hover-overlay">
          <img
            src="{% static 'images/buttonlogo.png' %}"
            alt="Logo"
            class="overlay-logo"
          />
        </div>
      </div>
      {% endfor %} {% else %}
      <div class="curved-square-wrapper">
        <img
          src="{% static 'images/img1.jpeg' %}"
          alt="Portfolio Image 1"
          class="curved-square"
        />
        <div class="hover-overlay">
          <img
            src="{% static 'images/buttonlogo.png' %}"
            alt="Logo"
            class="overlay-logo"
          />
        </div>
      </div>
      <div class="curved-square-wrapper">
        <img
          src="{% static 'images/img2.jpeg' %}"
          alt="Portfolio Image 2"
          class="curved-square"
        />
        <div class="hover-overlay">
          <img
            src="{% static 'images/buttonlogo.png' %}"
            alt="Logo"
            class="overlay-logo"
          />
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</section>

<section class="text-content">
  <p class="center-text">{{ team_member.bio }}</p>
</section>

<section class="hover-image-section">
  <div class="container">
    <div class="curved-square-wrapper">
      {% with portfolio_images|slice:"2:3"|first as center_image %} {% if
      center_image %}
      <img
        src="{{ center_image.image.url }}"
        alt="{{ center_image.title }}"
        class="hover-image"
      />
      {% else %}
      <img
        src="{% static 'images/img5.jpeg' %}"
        alt="Portfolio Image"
        class="hover-image"
      />
      {% endif %} {% endwith %}
      <div class="hover-overlay">
        <img
          src="{% static 'images/buttonlogo.png' %}"
          alt="Logo"
          class="overlay-logo"
        />
      </div>
    </div>
  </div>
</section>

<section class="infinite-images">
  <div class="container">
    <h3 class="section-title">{{ team_member.name }}'s Portfolio</h3>
    <div class="images-ticker-wrapper">
      <div class="images-right-to-left">
        {% if portfolio_images %} {% for i in "123456789012345678901234" %} {%
        for image in portfolio_images|slice:":6" %}
        <div class="curved-square-wrapper ticker-item">
          <img
            src="{{ image.image.url }}"
            alt="{{ image.title }}"
            class="ticker-image"
          />
          <div class="hover-overlay">
            <img
              src="{% static 'images/buttonlogo.png' %}"
              alt="Logo"
              class="overlay-logo"
            />
          </div>
        </div>
        {% endfor %} {% endfor %} {% else %} {% for i in
        "123456789012345678901234" %}
        <div class="curved-square-wrapper ticker-item">
          <img
            src="{% static 'images/img1.jpeg' %}"
            alt="Portfolio Image"
            class="ticker-image"
          />
          <div class="hover-overlay">
            <img
              src="{% static 'images/buttonlogo.png' %}"
              alt="Logo"
              class="overlay-logo"
            />
          </div>
        </div>
        <div class="curved-square-wrapper ticker-item">
          <img
            src="{% static 'images/img2.jpeg' %}"
            alt="Portfolio Image"
            class="ticker-image"
          />
          <div class="hover-overlay">
            <img
              src="{% static 'images/buttonlogo.png' %}"
              alt="Logo"
              class="overlay-logo"
            />
          </div>
        </div>
        {% endfor %} {% endif %}
      </div>
    </div>
  </div>
</section>

{% if related_members %}
<section class="related-members-section">
  <div class="container">
    <h2>Other {{ team_member.get_display_role }}s</h2>
    <div class="related-grid">
      {% for member in related_members %}
      <div
        class="related-member-card"
        onclick="window.location.href='{% url 'netfyre_app:team_member_detail' member.pk %}'"
      >
        {% if member.profile_image %}
        <img src="{{ member.profile_image.url }}" alt="{{ member.name }}" />
        {% else %}
        <img
          src="{% static 'images/default-avatar.png' %}"
          alt="{{ member.name }}"
        />
        {% endif %}
        <div class="member-info">
          <h4>{{ member.name }}</h4>
          <p>{{ member.get_display_role }}</p>
          <span class="experience">{{ member.years_experience }} years</span>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</section>
{% endif %}

<section class="back-to-team">
  <div class="container">
    <a href="{% url 'netfyre_app:team' %}" class="back-btn">← Back to Team</a>
  </div>
</section>

{% endblock %}
