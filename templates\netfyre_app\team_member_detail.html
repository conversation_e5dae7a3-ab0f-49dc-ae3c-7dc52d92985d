{% extends 'base.html' %} {% load static %} {% block title %}{{ team_member.name
}} - Team Member{% endblock %} {% block body_attributes %}
class="team-member-detail"{% endblock %} {% block extra_css %}
<link rel="stylesheet" href="{% static 'css/premium_team.css' %}" />
{% endblock %} {% block content %}
<!-- Dark Theme Team Member Profile -->
<section class="dark-member-profile">
  <div class="profile-container-dark">
    <!-- Breadcrumb -->
    <div class="breadcrumb-dark">
      <a href="{% url 'netfyre_app:home' %}">Home</a>
      <span class="separator">—</span>
      <a href="{% url 'netfyre_app:team' %}">Team</a>
      <span class="separator">—</span>
      <span class="current">{{ team_member.name }}</span>
    </div>

    <!-- Main Profile Content -->
    <div class="profile-content-dark">
      <!-- Left Side - Profile Image -->
      <div class="profile-image-section">
        {% if team_member.profile_image %}
        <img
          src="{{ team_member.profile_image.url }}"
          alt="{{ team_member.name }}"
          class="profile-image-dark"
        />
        {% else %}
        <div class="profile-placeholder-dark">
          <span class="profile-initials-dark"
            >{{ team_member.name|slice:":2"|upper }}</span
          >
        </div>
        {% endif %}
      </div>

      <!-- Right Side - Description and Info -->
      <div class="profile-description-section">
        <h1 class="member-name-dark">{{ team_member.name }}</h1>
        <p class="member-role-dark">{{ team_member.get_display_role }}</p>

        <div class="member-bio-dark">
          <p>{{ team_member.bio }}</p>
        </div>

        <div class="member-stats-dark">
          <div class="stat-item-dark">
            <span class="stat-number-dark"
              >{{ team_member.years_experience }}</span
            >
            <span class="stat-label-dark">Years Experience</span>
          </div>
          <div class="stat-item-dark">
            <span class="stat-number-dark"
              >{{ team_member.projects_completed|default:"50" }}+</span
            >
            <span class="stat-label-dark">Projects Completed</span>
          </div>
        </div>

        <!-- Contact Information -->
        {% if team_member.show_contact %}
        <div class="contact-section-dark">
          {% if team_member.email %}
          <a href="mailto:{{ team_member.email }}" class="contact-link-dark">
            <span>Email</span>
          </a>
          {% endif %} {% if team_member.linkedin_url %}
          <a
            href="{{ team_member.linkedin_url }}"
            target="_blank"
            class="contact-link-dark"
          >
            <span>LinkedIn</span>
          </a>
          {% endif %} {% if team_member.personal_website %}
          <a
            href="{{ team_member.personal_website }}"
            target="_blank"
            class="contact-link-dark"
          >
            <span>Website</span>
          </a>
          {% endif %}
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</section>

{% endblock %}
