{% extends 'base.html' %}
{% load static %}

{% block title %}{{ team_member.name }} - Team Member{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/team_member_detail.css' %}" />
{% endblock %}

{% block content %}
<!-- Team Member Header -->
<section class="team-member-header">
  <div class="container">
    <div class="header-content">
      <div class="breadcrumb">
        <a href="{% url 'netfyre_app:home' %}">Home</a> / 
        <a href="{% url 'netfyre_app:team' %}">Team</a> / 
        {{ team_member.name }}
      </div>
      <h1 class="member-name">{{ team_member.name }}</h1>
      <div class="member-meta">
        <span class="role">{{ team_member.get_display_role }}</span>
        <span class="experience">{{ team_member.years_experience }} years experience</span>
      </div>
    </div>
  </div>
</section>

<!-- Top Images Section - Two curved square images -->
<section class="top-images">
  <div class="curved-images">
    {% if portfolio_images %}
      {% with portfolio_images|slice:":2" as top_images %}
        {% for image in top_images %}
          <div class="curved-square-wrapper">
            <img
              src="{{ image.image.url }}"
              alt="{{ image.title }}"
              class="curved-square"
            />
            <div class="hover-overlay">
              <img
                src="{% static 'images/buttonlogo.png' %}"
                alt="Logo"
                class="overlay-logo"
              />
            </div>
          </div>
        {% endfor %}
        {% if top_images|length == 1 %}
          <div class="curved-square-wrapper">
            <img
              src="{% static 'images/img2.jpeg' %}"
              alt="Portfolio Image"
              class="curved-square"
            />
            <div class="hover-overlay">
              <img
                src="{% static 'images/buttonlogo.png' %}"
                alt="Logo"
                class="overlay-logo"
              />
            </div>
          </div>
        {% endif %}
      {% endwith %}
    {% else %}
      <div class="curved-square-wrapper">
        <img
          src="{% static 'images/img1.jpeg' %}"
          alt="Portfolio Image"
          class="curved-square"
        />
        <div class="hover-overlay">
          <img
            src="{% static 'images/buttonlogo.png' %}"
            alt="Logo"
            class="overlay-logo"
          />
        </div>
      </div>
      <div class="curved-square-wrapper">
        <img
          src="{% static 'images/img2.jpeg' %}"
          alt="Portfolio Image"
          class="curved-square"
        />
        <div class="hover-overlay">
          <img
            src="{% static 'images/buttonlogo.png' %}"
            alt="Logo"
            class="overlay-logo"
          />
        </div>
      </div>
    {% endif %}
  </div>
</section>

<!-- Black paragraph text in center -->
<section class="text-content">
  <p class="center-text">{{ team_member.bio }}</p>
</section>

<!-- Center image with hover animation -->
{% if portfolio_images %}
<section class="hover-image-section">
  <div class="container">
    <div class="curved-square-wrapper">
      {% with portfolio_images|slice:"2:3"|first as center_image %}
        {% if center_image %}
          <img
            src="{{ center_image.image.url }}"
            alt="{{ center_image.title }}"
            class="hover-image"
          />
        {% else %}
          <img
            src="{% static 'images/img5.jpeg' %}"
            alt="Portfolio Image"
            class="hover-image"
          />
        {% endif %}
      {% endwith %}
      <div class="hover-overlay">
        <img
          src="{% static 'images/buttonlogo.png' %}"
          alt="Logo"
          class="overlay-logo"
        />
      </div>
    </div>
  </div>
</section>
{% endif %}

<!-- Infinite moving images (right to left) - Framer Style -->
<section class="infinite-images">
  <div class="container">
    <h3 class="section-title">{{ team_member.name }}'s Portfolio</h3>
    <div class="images-ticker-wrapper">
      <div class="images-right-to-left">
        {% if portfolio_images %}
          {% for i in "123456789012345678901234" %}
            {% for image in portfolio_images|slice:":6" %}
              <div class="curved-square-wrapper ticker-item">
                <img
                  src="{{ image.image.url }}"
                  alt="{{ image.title }}"
                  class="ticker-image"
                />
                <div class="hover-overlay">
                  <img
                    src="{% static 'images/buttonlogo.png' %}"
                    alt="Logo"
                    class="overlay-logo"
                  />
                </div>
              </div>
            {% endfor %}
          {% endfor %}
        {% else %}
          {% for i in "123456789012345678901234" %}
            <div class="curved-square-wrapper ticker-item">
              <img
                src="{% static 'images/img1.jpeg' %}"
                alt="Portfolio Image"
                class="ticker-image"
              />
              <div class="hover-overlay">
                <img
                  src="{% static 'images/buttonlogo.png' %}"
                  alt="Logo"
                  class="overlay-logo"
                />
              </div>
            </div>
            <div class="curved-square-wrapper ticker-item">
              <img
                src="{% static 'images/img2.jpeg' %}"
                alt="Portfolio Image"
                class="ticker-image"
              />
              <div class="hover-overlay">
                <img
                  src="{% static 'images/buttonlogo.png' %}"
                  alt="Logo"
                  class="overlay-logo"
                />
              </div>
            </div>
          {% endfor %}
        {% endif %}
      </div>
    </div>
  </div>
</section>

<!-- Related Team Members -->
{% if related_members %}
<section class="related-members-section">
  <div class="container">
    <h2>Other {{ team_member.get_display_role }}s</h2>
    <div class="related-grid">
      {% for member in related_members %}
      <div
        class="related-member-card"
        onclick="window.location.href='{% url 'netfyre_app:team_member_detail' member.pk %}'"
      >
        {% if member.profile_image %}
        <img src="{{ member.profile_image.url }}" alt="{{ member.name }}" />
        {% else %}
        <img
          src="{% static 'images/default-avatar.png' %}"
          alt="{{ member.name }}"
        />
        {% endif %}
        <div class="member-info">
          <h4>{{ member.name }}</h4>
          <p>{{ member.get_display_role }}</p>
          <span class="experience">{{ member.years_experience }} years</span>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</section>
{% endif %}

<!-- Back to Team Button -->
<section class="back-to-team">
  <div class="container">
    <a href="{% url 'netfyre_app:team' %}" class="back-btn">← Back to Team</a>
  </div>
</section>

{% endblock %}
