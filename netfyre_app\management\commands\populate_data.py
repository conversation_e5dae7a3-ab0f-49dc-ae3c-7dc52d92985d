from django.core.management.base import BaseCommand
from netfyre_app.models import (
    Service, Testimonial, Project, CompanyLogo, 
    FAQ, SiteSettings, Counter
)
from datetime import date


class Command(BaseCommand):
    help = 'Populate the database with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Populating database with sample data...')

        # Create Site Settings
        site_settings, created = SiteSettings.objects.get_or_create(
            defaults={
                'site_title': 'NetFyre',
                'site_tagline': 'Digital Marketing Agency',
                'hero_title': 'Increase Your Reach, Explode Your Sales.',
                'hero_subtitle': 'Our tailored approach to brand success means better clicks, higher quality traffic, scroll-stopping ads, better ROAS and ultimately... more profit.',
                'contact_email': '<EMAIL>',
                'phone_number': '+****************',
                'address': '123 Business St, Marketing City, MC 12345',
            }
        )
        if created:
            self.stdout.write('✓ Site settings created')

        # Create Services
        services_data = [
            {
                'title': 'Web Development',
                'subtitle': 'Modern & Responsive',
                'icon': '🚀',
                'category': 'Development',
                'description': 'Custom web development solutions using the latest technologies.',
                'order': 1
            },
            {
                'title': 'Mobile UI/UX',
                'subtitle': 'User-Centered Design',
                'icon': '📱',
                'category': 'Design',
                'description': 'Beautiful and intuitive mobile app designs that users love.',
                'order': 2
            },
            {
                'title': 'AI Automation',
                'subtitle': 'Smart Solutions',
                'icon': '🤖',
                'category': 'AI',
                'description': 'Intelligent automation solutions to streamline your business.',
                'order': 3
            },
            {
                'title': 'Brand Identity',
                'subtitle': 'Visual Storytelling',
                'icon': '🎨',
                'category': 'Branding',
                'description': 'Complete brand identity packages that tell your story.',
                'order': 4
            },
            {
                'title': 'Digital Marketing',
                'subtitle': 'Growth Strategy',
                'icon': '📊',
                'category': 'Marketing',
                'description': 'Data-driven marketing strategies that deliver results.',
                'order': 5
            },
            {
                'title': 'Video Production',
                'subtitle': 'Cinematic Quality',
                'icon': '🎬',
                'category': 'Video',
                'description': 'Professional video production for all your marketing needs.',
                'order': 6
            }
        ]

        for service_data in services_data:
            service, created = Service.objects.get_or_create(
                title=service_data['title'],
                defaults=service_data
            )
            if created:
                self.stdout.write(f'✓ Service created: {service.title}')

        # Create Testimonials
        testimonials_data = [
            {
                'client_name': 'Emily Thompson',
                'client_title': 'Marketing Director',
                'client_company': 'Tech Innovators Inc.',
                'testimonial_text': 'Working with NetFyre was a game-changer for us. Their expertise in UX/UI design transformed our user experience, resulting in a 30% increase in user engagement. We couldn\'t be happier!',
                'rating': 5,
                'is_featured': True
            },
            {
                'client_name': 'John Smith',
                'client_title': 'CEO',
                'client_company': 'Digital Solutions Co.',
                'testimonial_text': 'NetFyre\'s digital marketing strategies helped us scale our business to new heights. Their data-driven approach and creative campaigns delivered exceptional ROI.',
                'rating': 5,
                'is_featured': True
            },
            {
                'client_name': 'Sarah Johnson',
                'client_title': 'Founder',
                'client_company': 'E-commerce Startup',
                'testimonial_text': 'The team at NetFyre understood our vision perfectly. They created a brand identity that truly represents our values and resonates with our target audience.',
                'rating': 5
            },
            {
                'client_name': 'Michael Brown',
                'client_title': 'Product Manager',
                'client_company': 'SaaS Company',
                'testimonial_text': 'NetFyre\'s web development skills are top-notch. They delivered a fast, responsive, and user-friendly website that exceeded our expectations.',
                'rating': 5
            },
            {
                'client_name': 'Lisa Davis',
                'client_title': 'Creative Director',
                'client_company': 'Media Agency',
                'testimonial_text': 'The video production quality from NetFyre is outstanding. Their creative storytelling and technical expertise brought our brand story to life beautifully.',
                'rating': 5
            }
        ]

        for testimonial_data in testimonials_data:
            testimonial, created = Testimonial.objects.get_or_create(
                client_name=testimonial_data['client_name'],
                client_company=testimonial_data['client_company'],
                defaults=testimonial_data
            )
            if created:
                self.stdout.write(f'✓ Testimonial created: {testimonial.client_name}')

        # Create Projects
        projects_data = [
            {
                'title': 'E-commerce Platform Redesign',
                'description': 'Complete redesign of a major e-commerce platform with focus on user experience and conversion optimization.',
                'client': 'ShopTech Inc.',
                'technologies': 'React, Node.js, MongoDB, Stripe API',
                'completion_date': date(2024, 12, 15),
                'is_featured': True,
                'order': 1
            },
            {
                'title': 'Mobile Banking App',
                'description': 'Secure and intuitive mobile banking application with advanced features and biometric authentication.',
                'client': 'FinanceFirst Bank',
                'technologies': 'React Native, Firebase, Biometric APIs',
                'completion_date': date(2024, 11, 30),
                'is_featured': True,
                'order': 2
            },
            {
                'title': 'AI-Powered Analytics Dashboard',
                'description': 'Advanced analytics dashboard with AI-powered insights and real-time data visualization.',
                'client': 'DataCorp Analytics',
                'technologies': 'Python, TensorFlow, D3.js, PostgreSQL',
                'completion_date': date(2024, 10, 20),
                'is_featured': True,
                'order': 3
            }
        ]

        for project_data in projects_data:
            project, created = Project.objects.get_or_create(
                title=project_data['title'],
                defaults=project_data
            )
            if created:
                self.stdout.write(f'✓ Project created: {project.title}')

        # Create Counters
        counters_data = [
            {
                'label': 'TOTAL ROI',
                'value': 39987,
                'prefix': '$',
                'suffix': '+',
                'order': 1
            },
            {
                'label': 'Daily Spend',
                'value': 500,
                'prefix': '$',
                'suffix': '+',
                'order': 2
            },
            {
                'label': 'Clients Served',
                'value': 21,
                'prefix': '',
                'suffix': '',
                'order': 3
            }
        ]

        for counter_data in counters_data:
            counter, created = Counter.objects.get_or_create(
                label=counter_data['label'],
                defaults=counter_data
            )
            if created:
                self.stdout.write(f'✓ Counter created: {counter.label}')

        # Create FAQs
        faqs_data = [
            {
                'question': 'How do we get started?',
                'answer': 'Super simple. Just hit the Let\'s Talk button or send me a quick email. Tell me a bit about your project, and I\'ll get back to you within 24 hours to discuss the next steps.',
                'order': 1
            },
            {
                'question': 'How long does a project take?',
                'answer': 'One-pagers usually take 1–2 weeks. Full websites (3+ pages) usually take 3–4 weeks. I\'ll give you a clear timeline after we chat about your project needs.',
                'order': 2
            },
            {
                'question': 'Do you work with agencies or white-label?',
                'answer': 'Yep, I do! I\'m open to collaborating silently as part of your team. I can jump in as your design partner or handle specific parts of the project under NDA or white-label terms.',
                'order': 3
            },
            {
                'question': 'Can I pay in milestones?',
                'answer': 'For sure! I usually do 50% upfront and 50% before final delivery, but I\'m happy to adjust based on what works best for both of us. Just let me know your situation.',
                'order': 4
            },
            {
                'question': 'Can you just design it and not build it?',
                'answer': 'Absolutely! If you already have a dev team or just need the Figma design, I can deliver clean, organized design files ready for handoff with clear notes for your developers.',
                'order': 5
            }
        ]

        for faq_data in faqs_data:
            faq, created = FAQ.objects.get_or_create(
                question=faq_data['question'],
                defaults=faq_data
            )
            if created:
                self.stdout.write(f'✓ FAQ created: {faq.question[:50]}...')

        self.stdout.write(
            self.style.SUCCESS('Successfully populated database with sample data!')
        )
