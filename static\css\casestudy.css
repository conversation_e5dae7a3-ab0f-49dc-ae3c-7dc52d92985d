/* Basic Reset and Body Styling */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Arial", sans-serif;
  background-color: #ffffff !important;
  color: #000000;
  overflow-x: hidden;
}

/* CRITICAL: Hide all home page elements that might interfere */
.hero-section,
.hero-gallery,
.hero-bg-text,
.bg-text-row,
.gallery-column,
.gallery-track,
.gallery-img {
  display: none !important;
  visibility: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  width: 0 !important;
  height: 0 !important;
  opacity: 0 !important;
  z-index: -1 !important;
}

/* Ensure case study page content is visible and positioned correctly */
.case-study-page {
  position: relative !important;
  z-index: 1000 !important;
  width: 100% !important;
  min-height: 100vh !important;
  background: white !important;
  display: block !important;
  visibility: visible !important;
}

/* Force case study sections to be visible */
.case-study-page section {
  display: block !important;
  visibility: visible !important;
  position: relative !important;
  z-index: 1001 !important;
}

/* Top Images Section - Two curved square images */
.top-images {
  padding: 80px 0 80px 0;
  background: white;
  display: flex;
  justify-content: center;
  align-items: center;
  perspective: 1000px;
}

.curved-images {
  display: flex;
  gap: 90px;
  justify-content: center;
  align-items: center;
  padding: 10px;
  transform-style: preserve-3d;
}

.curved-square {
  width: 450px;
  height: 450px;
  object-fit: cover;
  border-radius: 25px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  cursor: pointer;
  overflow: hidden;
  transform: scale(1);
}

.curved-square:hover {
  /* Remove scale and move effects */
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  filter: brightness(1.1) contrast(1.05);
}

.curved-square::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 25px;
  opacity: 0;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: none;
  z-index: 1;
  transform: scale(1.1);
}

.curved-square::after {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  color: white;
  font-weight: bold;
  text-align: center;
  opacity: 0;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: none;
  z-index: 2;
  width: 80%;
  line-height: 1.4;
}

/* First curved square - Process text overlay */
.curved-square:first-child::after {
  content: "OUR THREE STEP PROCESS\A\AEngineered for Power.\ADesigned for Style.\AMade to Last";
  white-space: pre-line;
  font-size: 20px;
  color: white;
}

/* Second curved square - Logo overlay behind content */
.curved-square:last-child::after {
  content: "";
  background-image: url("../images/buttonlogo.png");
  background-size: 120px 120px;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  transform: none;
  opacity: 0;
  z-index: 1;
  transition: opacity 0.4s ease, transform 0.4s ease;
}

/* Only show dark overlay and text on first image hover */
.curved-square:first-child:hover::before {
  opacity: 1;
  transform: scale(1);
}

.curved-square:first-child:hover::after {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

/* Only show logo overlay on second image hover */
.curved-square:last-child:hover::after {
  opacity: 0.7;
  transform: scale(1.05);
}

/* Prevent dark overlay on second image */
.curved-square:last-child::before {
  display: none;
}

/* Hover rules are now specific to each image above */

/* Text Content Section */
.text-content {
  padding: 60px 20px;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 100%;
}

.center-text {
  max-width: 800px;
  text-align: center;
  font-size: 18px;
  line-height: 1.8;
  color: #000000;
  font-weight: 800;
  margin: 0 auto;
  font-style: bold;
  width: 100%;
}

/* Hover Image Section */
.hover-image-section {
  padding: 60px 20px;
  background-color: #f8f9fa;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 100%;
}

.hover-image {
  width: 100%;
  max-width: 1000px;
  height: 400px;
  object-fit: cover;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  margin: 0 auto;
  display: block;
  transform: scale(1);
}

.hover-image:hover {
  transform: scale(1.08) translateY(-10px);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
  filter: brightness(1.1) contrast(1.05);
}

/* Video Section */
.video-section {
  width: 100%;
  padding: 60px 20px;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.center-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 15px;
  object-fit: cover;
  border: none;
}

.video-section .video-wrapper {
  position: relative;
  width: 100%;
  max-width: 1000px;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  margin: 0 auto;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  background: #000;
}

/* Infinite Moving Images Section (Right to Left) - Framer Style */
.infinite-images {
  padding: 40px 0;
  background-color: #ffffff;
  overflow: hidden;
  position: relative;
  width: 100%;
}

.infinite-images h3 {
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  color: #000;
  margin-bottom: 30px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.images-right-to-left {
  display: flex;
  gap: 20px;
  animation: scroll-left 120s linear infinite;
  width: max-content;
  will-change: transform;
}

.images-right-to-left img {
  width: 200px;
  height: 140px;
  object-fit: cover;
  border-radius: 12px;
  flex-shrink: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.images-right-to-left img:hover {
  transform: scale(1.05);
}

/* Infinite Moving Logos Section (Left to Right) - Framer Style */
.infinite-logos {
  padding: 40px 0 60px 0;
  background-color: #ffffff;
  overflow: hidden;
  position: relative;
  width: 100%;
}

.infinite-logos h4 {
  text-align: center;
  font-size: 16px;
  font-weight: normal;
  color: #666;
  margin-bottom: 30px;
  text-transform: lowercase;
  letter-spacing: 1px;
}

.logos-left-to-right {
  display: flex;
  gap: 60px;
  animation: scroll-right 120s linear infinite;
  width: max-content;
  will-change: transform;
}

.logo {
  background: transparent;
  color: #000;
  padding: 0;
  border-radius: 0;
  font-weight: 600;
  font-size: 18px;
  white-space: nowrap;
  flex-shrink: 0;
  box-shadow: none;
  text-align: center;
  min-width: auto;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.logo:hover {
  opacity: 1;
}

.trusted-logo-img {
  height: 40px;
  width: auto;
  max-width: 140px;
  object-fit: contain;
  flex-shrink: 0;
  background: transparent;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  opacity: 0.6;
  transition: opacity 0.3s ease;
  filter: grayscale(100%);
}

.trusted-logo-img:hover {
  opacity: 1;
  filter: grayscale(0%);
}

/* Back to Home Section */
.back-home {
  padding: 40px 20px;
  background-color: #f8f9fa;
  display: flex;
  justify-content: center;
  align-items: center;
}

.back-btn {
  background: linear-gradient(45deg, #000000, #333333);
  color: white;
  padding: 15px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: bold;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.back-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #333333, #555555);
}

/* Framer-style Infinite Scroll Animations - Improved for seamless loop */
@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-right {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .curved-images {
    flex-direction: column;
    gap: 20px;
  }

  .curved-square {
    width: 300px;
    height: 300px;
  }

  .center-text {
    font-size: 16px;
    padding: 0 15px;
    margin: 0 auto;
    width: 100%;
    text-align: center;
  }

  .hover-image {
    width: 95%;
    max-width: 95%;
    height: 250px;
    margin: 0 auto;
    display: block;
  }

  .images-right-to-left img {
    width: 150px;
    height: 100px;
  }

  .video-section .video-wrapper {
    width: 95%;
    max-width: 95%;
    padding-bottom: 56.25%; /* Maintain 16:9 aspect ratio */
    border-radius: 12px;
  }

  .center-video {
    border-radius: 12px;
  }

  .logo {
    font-size: 14px;
  }

  .infinite-images h3 {
    font-size: 24px;
    margin-bottom: 20px;
  }

  .infinite-logos h4 {
    font-size: 14px;
    margin-bottom: 20px;
  }

  /* Mobile responsive navigation buttons */
  .casestudy-navigation {
    flex-direction: column;
    gap: 20px;
    padding: 30px 20px;
    text-align: center;
  }

  .back-btn,
  .contact-btn {
    width: 100%;
    max-width: 280px;
    padding: 15px 20px;
    font-size: 14px;
    text-align: center;
    margin: 0;
    display: block;
  }

  .contact-btn {
    margin-top: 0 !important;
  }

  /* Mobile header adjustments */
  .casestudy-title {
    font-size: 28px;
  }

  .casestudy-meta {
    flex-direction: column;
    gap: 10px;
  }
}

/* Navbar Specific Styles for Case Study Page */
.navbar {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 9999 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
  padding: 15px 0 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.nav-container {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  max-width: 1200px !important;
  width: 100% !important;
  padding: 0 20px !important;
}

.logo {
  font-size: 24px !important;
  font-weight: bold !important;
  color: #000 !important;
  text-decoration: none !important;
}

.nav-links {
  display: flex !important;
  gap: 30px !important;
  list-style: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.nav-link {
  color: #000 !important;
  text-decoration: none !important;
  font-weight: 500 !important;
  transition: color 0.3s ease !important;
}

.nav-link:hover {
  color: #666 !important;
}

/* Case Study Header */
.casestudy-header {
  padding: 140px 0 40px 0;
  background: white;
  text-align: center;
  margin-top: 0;
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.breadcrumb {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.breadcrumb a {
  color: #000;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.casestudy-title {
  font-size: 42px;
  font-weight: bold;
  color: #000;
  margin-bottom: 20px;
  line-height: 1.2;
}

.casestudy-meta {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 20px;
}

.casestudy-meta .client {
  font-size: 16px;
  font-weight: bold;
  color: #000;
}

.casestudy-meta .category {
  font-size: 14px;
  color: #666;
  background: #f0f0f0;
  padding: 6px 12px;
  border-radius: 15px;
}

/* Related Case Studies */
.related-casestudies {
  padding: 60px 20px;
  background: #f8f9fa;
}

.related-casestudies h3 {
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  color: #000;
  margin-bottom: 40px;
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

.related-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.related-card:hover {
  transform: translateY(-5px);
}

.related-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.related-card h4 {
  padding: 20px 20px 10px;
  font-size: 20px;
  color: #000;
}

.related-card p {
  padding: 0 20px 10px;
  color: #666;
}

.related-card a {
  display: block;
  padding: 10px 20px 20px;
  color: #000;
  text-decoration: none;
  font-weight: bold;
}

/* Case Study Navigation */
.casestudy-navigation {
  padding: 40px 20px;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1000px;
  margin: 0 auto;
}

.contact-btn {
  background: linear-gradient(45deg, #000000, #333333);
  color: white;
  padding: 15px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: bold;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #333333, #555555);
}

/* Additional responsive breakpoints for video */
@media (max-width: 480px) {
  .video-section .video-wrapper {
    width: 100%;
    padding-bottom: 56.25%; /* Maintain 16:9 aspect ratio */
    border-radius: 10px;
    margin: 0 auto;
  }

  .center-video {
    border-radius: 10px;
  }
}

@media (min-width: 1200px) {
  .video-section .video-wrapper {
    max-width: 1200px;
    padding-bottom: 50.625%; /* Slightly wider aspect ratio for large screens */
  }
}
