/* Premium Team Member Detail - Ultra Minimal Design */

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  background-color: #ffffff;
  color: #1a1a1a;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Premium Cover Photo Section */
.premium-cover {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.cover-image-wrapper {
  position: relative;
  height: 60vh;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 100%
  );
}

/* Profile Section */
.profile-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 40px;
  background: #ffffff;
  position: relative;
  margin-top: -120px;
  z-index: 2;
}

.profile-container {
  max-width: 1200px;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 80px;
}

.profile-photo-wrapper {
  flex-shrink: 0;
}

.profile-photo {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  object-fit: cover;
  border: 6px solid #ffffff;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.profile-placeholder {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6c757d, #495057);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 6px solid #ffffff;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.profile-initials {
  font-size: 48px;
  font-weight: 300;
  color: white;
  letter-spacing: 2px;
}

.profile-info {
  flex: 1;
}

.breadcrumb-minimal {
  font-size: 14px;
  color: #666;
  margin-bottom: 60px;
  font-weight: 400;
}

.breadcrumb-minimal a {
  color: #666;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-minimal a:hover {
  color: #000;
}

.separator {
  margin: 0 12px;
  color: #ccc;
}

.current {
  color: #000;
  font-weight: 500;
}

.hero-main {
  margin-bottom: 80px;
}

.member-name-premium {
  font-size: clamp(48px, 8vw, 96px);
  font-weight: 300;
  color: #000;
  margin-bottom: 20px;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.member-role-premium {
  font-size: 24px;
  color: #666;
  font-weight: 400;
  margin-bottom: 60px;
  letter-spacing: 0.01em;
}

.member-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  margin-top: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48px;
  font-weight: 200;
  color: #000;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 500;
}

.stat-divider {
  width: 1px;
  height: 60px;
  background: #e0e0e0;
}

/* Premium Bio Section */
.premium-bio {
  padding: 120px 0;
  background: #fafafa;
}

.bio-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.bio-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 80px;
  align-items: start;
}

.bio-text-wrapper {
}

.bio-title {
  font-size: 36px;
  font-weight: 300;
  color: #000;
  margin-bottom: 30px;
  letter-spacing: -0.01em;
}

.bio-text {
  font-size: 18px;
  line-height: 1.8;
  color: #444;
  font-weight: 300;
}

/* Contact Information */
.contact-info {
  background: #ffffff;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.contact-item {
  margin-bottom: 24px;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 6px;
}

.contact-value {
  font-size: 16px;
  color: #000;
  text-decoration: none;
  font-weight: 400;
  transition: color 0.3s ease;
}

.contact-value:hover {
  color: #007bff;
}

/* Premium Skills Section */
.premium-skills {
  padding: 120px 0;
  background: #ffffff;
}

.skills-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.skills-content {
  text-align: center;
}

.skills-title {
  font-size: 36px;
  font-weight: 300;
  color: #000;
  margin-bottom: 60px;
  letter-spacing: -0.01em;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.skill-tag {
  padding: 12px 24px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 400;
  color: #495057;
  transition: all 0.3s ease;
  cursor: default;
}

.skill-tag:hover {
  background: #000;
  color: #fff;
  border-color: #000;
}

/* Premium Portfolio Section */
.premium-portfolio {
  padding: 120px 0;
  background: #fafafa;
}

.portfolio-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.portfolio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 80px;
}

.portfolio-title {
  font-size: 48px;
  font-weight: 300;
  color: #000;
  letter-spacing: -0.02em;
}

.portfolio-count {
  font-size: 16px;
  color: #666;
  font-weight: 400;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
}

.portfolio-item {
  position: relative;
  aspect-ratio: 4/3;
  overflow: hidden;
  border-radius: 8px;
  background: #fff;
  cursor: pointer;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.portfolio-item:hover {
  transform: translateY(-8px);
}

.portfolio-image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.portfolio-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.portfolio-item:hover .portfolio-image {
  transform: scale(1.05);
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.4));
  opacity: 0;
  transition: opacity 0.4s ease;
  display: flex;
  align-items: flex-end;
  padding: 40px;
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.overlay-content {
  color: white;
}

.project-number {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.8;
  display: block;
  margin-bottom: 8px;
  letter-spacing: 0.1em;
}

.project-title {
  font-size: 24px;
  font-weight: 400;
  margin: 0;
  letter-spacing: -0.01em;
}

/* Premium Expertise Section */
.premium-expertise {
  padding: 120px 0;
  background: #ffffff;
}

.expertise-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.expertise-content {
  text-align: center;
}

.expertise-title {
  font-size: 48px;
  font-weight: 300;
  color: #000;
  margin-bottom: 80px;
  letter-spacing: -0.02em;
}

.expertise-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 60px;
  margin-top: 60px;
}

.expertise-item {
  text-align: center;
}

.expertise-icon {
  width: 80px;
  height: 80px;
  border: 1px solid #e0e0e0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30px;
  font-size: 18px;
  font-weight: 300;
  color: #666;
  transition: all 0.3s ease;
}

.expertise-item:hover .expertise-icon {
  border-color: #000;
  color: #000;
  transform: translateY(-4px);
}

.expertise-name {
  font-size: 20px;
  font-weight: 400;
  color: #000;
  margin-bottom: 12px;
}

.expertise-desc {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  font-weight: 300;
}

/* Premium Contact Section */
.premium-contact {
  padding: 120px 0;
  background: #000;
  color: white;
}

.contact-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 40px;
  text-align: center;
}

.contact-title {
  font-size: 48px;
  font-weight: 300;
  margin-bottom: 20px;
  letter-spacing: -0.02em;
}

.contact-subtitle {
  font-size: 20px;
  color: #ccc;
  margin-bottom: 60px;
  font-weight: 300;
}

.contact-actions {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
  padding: 16px 32px;
  border-radius: 6px;
  text-decoration: none;
  font-size: 16px;
  font-weight: 400;
  transition: all 0.3s ease;
  display: inline-block;
  letter-spacing: 0.01em;
}

.btn-primary {
  background: white;
  color: #000;
  border: 1px solid white;
}

.btn-primary:hover {
  background: transparent;
  color: white;
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 1px solid #333;
}

.btn-secondary:hover {
  border-color: white;
  background: white;
  color: #000;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .profile-container {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }

  .bio-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .profile-section {
    padding: 40px 20px;
    margin-top: -80px;
  }

  .bio-container,
  .skills-container,
  .contact-container {
    padding: 0 20px;
  }

  .cover-image-wrapper {
    height: 40vh;
  }

  .profile-photo,
  .profile-placeholder {
    width: 150px;
    height: 150px;
  }

  .profile-initials {
    font-size: 36px;
  }

  .member-name-premium {
    font-size: 36px;
  }

  .member-stats {
    flex-direction: column;
    gap: 20px;
  }

  .stat-divider {
    width: 60px;
    height: 1px;
  }

  .bio-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .contact-info {
    padding: 30px;
  }

  .skills-list {
    gap: 12px;
  }

  .skill-tag {
    padding: 10px 20px;
    font-size: 13px;
  }

  .contact-actions {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .btn-primary,
  .btn-secondary {
    width: 200px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .profile-section {
    padding: 30px 15px;
  }

  .bio-container,
  .skills-container,
  .contact-container {
    padding: 0 15px;
  }

  .member-name-premium {
    font-size: 28px;
  }

  .bio-title,
  .skills-title,
  .contact-title {
    font-size: 28px;
  }

  .contact-info {
    padding: 20px;
  }
}
