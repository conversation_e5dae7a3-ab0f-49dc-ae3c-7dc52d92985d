/* Dark Theme Team Member Detail Page */

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Only apply dark theme to team member detail pages */
.team-member-detail-page body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  background-color: #0a0a0a;
  color: #ffffff;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Dark Theme Profile Section */
.dark-member-profile {
  min-height: 100vh;
  background: #0a0a0a;
  padding: 80px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-container-dark {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: 0 40px;
}

/* Breadcrumb */
.breadcrumb-dark {
  margin-bottom: 60px;
  text-align: left;
}

.breadcrumb-dark a {
  color: #888;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.breadcrumb-dark a:hover {
  color: #ffb600;
}

.breadcrumb-dark .separator {
  color: #444;
  margin: 0 12px;
}

.breadcrumb-dark .current {
  color: #fff;
  font-weight: 500;
}

/* Main Profile Content Layout */
.profile-content-dark {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 80px;
  align-items: center;
}

/* Left Side - Profile Image */
.profile-image-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-image-dark {
  width: 350px;
  height: 350px;
  border-radius: 20px;
  object-fit: cover;
  border: 3px solid #222;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.profile-placeholder-dark {
  width: 350px;
  height: 350px;
  border-radius: 20px;
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #222;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.profile-initials-dark {
  font-size: 80px;
  font-weight: 300;
  color: #666;
  letter-spacing: 4px;
}

/* Right Side - Description and Info */
.profile-description-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 30px;
}

.member-name-dark {
  font-size: clamp(48px, 6vw, 72px);
  font-weight: 300;
  color: #ffffff;
  margin-bottom: 16px;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.member-role-dark {
  font-size: 24px;
  color: #ffb600;
  font-weight: 500;
  margin-bottom: 40px;
  letter-spacing: 0.01em;
}

.member-bio-dark {
  margin-bottom: 40px;
}

.member-bio-dark p {
  font-size: 18px;
  line-height: 1.7;
  color: #cccccc;
  font-weight: 300;
  margin: 0;
}

.member-stats-dark {
  display: flex;
  gap: 60px;
  margin-bottom: 40px;
}

.stat-item-dark {
  text-align: left;
}

.stat-number-dark {
  display: block;
  font-size: 48px;
  font-weight: 200;
  color: #ffffff;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label-dark {
  font-size: 14px;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 500;
}

/* Contact Section */
.contact-section-dark {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.contact-link-dark {
  padding: 12px 24px;
  background: rgba(255, 182, 0, 0.1);
  border: 1px solid rgba(255, 182, 0, 0.3);
  border-radius: 8px;
  color: #ffb600;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-block;
}

.contact-link-dark:hover {
  background: rgba(255, 182, 0, 0.2);
  border-color: #ffb600;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .profile-container-dark {
    padding: 0 30px;
  }

  .profile-content-dark {
    grid-template-columns: 300px 1fr;
    gap: 60px;
  }

  .profile-image-dark,
  .profile-placeholder-dark {
    width: 280px;
    height: 280px;
  }

  .profile-initials-dark {
    font-size: 60px;
  }

  .member-name-dark {
    font-size: clamp(36px, 5vw, 56px);
  }

  .member-role-dark {
    font-size: 20px;
  }

  .member-stats-dark {
    gap: 40px;
  }

  .stat-number-dark {
    font-size: 36px;
  }
}

@media (max-width: 768px) {
  .dark-member-profile {
    padding: 60px 0;
  }

  .profile-container-dark {
    padding: 0 20px;
  }

  .breadcrumb-dark {
    margin-bottom: 40px;
    text-align: center;
  }

  .profile-content-dark {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .profile-image-dark,
  .profile-placeholder-dark {
    width: 250px;
    height: 250px;
    margin: 0 auto;
  }

  .profile-initials-dark {
    font-size: 50px;
  }

  .member-name-dark {
    font-size: clamp(32px, 8vw, 48px);
  }

  .member-role-dark {
    font-size: 18px;
  }

  .member-bio-dark p {
    font-size: 16px;
  }

  .member-stats-dark {
    justify-content: center;
    gap: 30px;
  }

  .stat-item-dark {
    text-align: center;
  }

  .stat-number-dark {
    font-size: 32px;
  }

  .contact-section-dark {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .dark-member-profile {
    padding: 40px 0;
  }

  .profile-container-dark {
    padding: 0 15px;
  }

  .profile-content-dark {
    gap: 30px;
  }

  .profile-image-dark,
  .profile-placeholder-dark {
    width: 200px;
    height: 200px;
  }

  .profile-initials-dark {
    font-size: 40px;
  }

  .member-name-dark {
    font-size: clamp(28px, 8vw, 36px);
  }

  .member-role-dark {
    font-size: 16px;
  }

  .member-bio-dark p {
    font-size: 15px;
  }

  .member-stats-dark {
    flex-direction: column;
    gap: 20px;
  }

  .stat-number-dark {
    font-size: 28px;
  }

  .stat-label-dark {
    font-size: 12px;
  }

  .contact-section-dark {
    flex-direction: column;
    align-items: center;
  }

  .contact-link-dark {
    width: 100%;
    max-width: 200px;
    text-align: center;
  }
}

/* End of Dark Theme Team Member Detail Page */
