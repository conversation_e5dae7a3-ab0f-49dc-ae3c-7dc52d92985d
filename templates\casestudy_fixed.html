{% extends 'base.html' %}
{% load static %}

{% block title %}Case Study{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/casestudy.css' %}" />
{% endblock %}

{% block content %}
<div class="case-study-page">
  <!-- Case Study Header -->
  <section class="casestudy-header">
    <div class="container">
      <h1 class="casestudy-title">
        {% if case_study.title %}
          {{ case_study.title }}
        {% else %}
          Case Study
        {% endif %}
      </h1>
      {% if case_study.subtitle %}
        <p class="casestudy-subtitle">{{ case_study.subtitle }}</p>
      {% endif %}
    </div>
  </section>

  <!-- Top section with two curved square images -->
  <section class="top-images">
    <div class="container">
      <div class="curved-images">
        {% if case_study.image_1 %}
        <div class="curved-square-wrapper">
          <img
            src="{{ case_study.image_1.url }}"
            alt="{{ case_study.image_1_alt }}"
            class="curved-square"
          />
          <div class="hover-overlay">
            <img src="{% static 'images/buttonlogo.png' %}" alt="Logo" class="overlay-logo" />
          </div>
        </div>
        {% else %}
        <div class="curved-square-wrapper">
          <img
            src="{% static 'images/img1.jpeg' %}"
            alt="Case Study Image 1"
            class="curved-square"
          />
          <div class="hover-overlay">
            <img src="{% static 'images/buttonlogo.png' %}" alt="Logo" class="overlay-logo" />
          </div>
        </div>
        {% endif %}
        
        {% if case_study.image_2 %}
        <div class="curved-square-wrapper">
          <img
            src="{{ case_study.image_2.url }}"
            alt="{{ case_study.image_2_alt }}"
            class="curved-square"
          />
          <div class="hover-overlay">
            <img src="{% static 'images/buttonlogo.png' %}" alt="Logo" class="overlay-logo" />
          </div>
        </div>
        {% else %}
        <div class="curved-square-wrapper">
          <img
            src="{% static 'images/img2.jpeg' %}"
            alt="Case Study Image 2"
            class="curved-square"
          />
          <div class="hover-overlay">
            <img src="{% static 'images/buttonlogo.png' %}" alt="Logo" class="overlay-logo" />
          </div>
        </div>
        {% endif %}
      </div>
    </div>
  </section>

  <!-- Black paragraph text in center -->
  <section class="text-content">
    <div class="container">
      <p class="center-text">
        {% if case_study.description %}
          {{ case_study.description }}
        {% else %}
          This is a beautiful paragraph text displayed in black color, perfectly
          centered below the curved square images. It demonstrates elegant
          typography and layout design.
        {% endif %}
      </p>
    </div>
  </section>

  <!-- Center image with hover animation -->
  <section class="hover-image-section">
    <div class="container">
      <div class="curved-square-wrapper">
        {% if case_study.hover_image %}
        <img
          src="{{ case_study.hover_image.url }}"
          alt="{{ case_study.hover_image_alt }}"
          class="hover-image"
        />
        {% else %}
        <img
          src="{% static 'images/img5.jpeg' %}"
          alt="Hover Image"
          class="hover-image"
        />
        {% endif %}
        <div class="hover-overlay">
          <img src="{% static 'images/buttonlogo.png' %}" alt="Logo" class="overlay-logo" />
        </div>
      </div>
    </div>
  </section>

  <!-- Video section -->
  <section class="video-section">
    <div class="container">
      <div class="video-wrapper">
        <video
          controls
          {% if case_study.video_autoplay %}autoplay{% endif %}
          {% if case_study.video_muted %}muted{% endif %}
          {% if case_study.video_loop %}loop{% endif %}
          playsinline
          class="center-video"
          {% if case_study.video_poster %}poster="{{ case_study.video_poster.url }}"{% endif %}
        >
          <source
            src="{% if case_study.video_file %}{{ case_study.video_file.url }}{% elif case_study.video_url %}{{ case_study.video_url }}{% else %}https://www.w3schools.com/html/mov_bbb.mp4{% endif %}"
            type="video/mp4"
          />
          Your browser does not support the video tag.
        </video>
      </div>
    </div>
  </section>

  <!-- Infinite moving images (right to left) - Framer Style -->
  <section class="infinite-images">
    <div class="container">
      <h3 class="section-title">Works For Netfyre</h3>
      <div class="images-ticker-wrapper">
        <div class="images-right-to-left">
          <!-- Repeat images multiple times for seamless scroll -->
          {% for i in "123456789012345678901234" %}
          <div class="curved-square-wrapper ticker-item">
            <img src="{% static 'images/img1.jpeg' %}" alt="Portfolio Image" class="ticker-image" />
            <div class="hover-overlay">
              <img src="{% static 'images/buttonlogo.png' %}" alt="Logo" class="overlay-logo" />
            </div>
          </div>
          <div class="curved-square-wrapper ticker-item">
            <img src="{% static 'images/img2.jpeg' %}" alt="Portfolio Image" class="ticker-image" />
            <div class="hover-overlay">
              <img src="{% static 'images/buttonlogo.png' %}" alt="Logo" class="overlay-logo" />
            </div>
          </div>
          <div class="curved-square-wrapper ticker-item">
            <img src="{% static 'images/img3.jpeg' %}" alt="Portfolio Image" class="ticker-image" />
            <div class="hover-overlay">
              <img src="{% static 'images/buttonlogo.png' %}" alt="Logo" class="overlay-logo" />
            </div>
          </div>
          <div class="curved-square-wrapper ticker-item">
            <img src="{% static 'images/img4.jpeg' %}" alt="Portfolio Image" class="ticker-image" />
            <div class="hover-overlay">
              <img src="{% static 'images/buttonlogo.png' %}" alt="Logo" class="overlay-logo" />
            </div>
          </div>
          <div class="curved-square-wrapper ticker-item">
            <img src="{% static 'images/img5.jpeg' %}" alt="Portfolio Image" class="ticker-image" />
            <div class="hover-overlay">
              <img src="{% static 'images/buttonlogo.png' %}" alt="Logo" class="overlay-logo" />
            </div>
          </div>
          <div class="curved-square-wrapper ticker-item">
            <img src="{% static 'images/img6.jpeg' %}" alt="Portfolio Image" class="ticker-image" />
            <div class="hover-overlay">
              <img src="{% static 'images/buttonlogo.png' %}" alt="Logo" class="overlay-logo" />
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
    </div>
  </section>

  <!-- Logos moving in opposite direction (left to right) - Framer Style -->
  <section class="infinite-logos">
    <div class="container">
      <h4 class="section-subtitle">trusted by:</h4>
      <div class="logos-ticker-wrapper">
        <div class="logos-left-to-right">
          <!-- Repeat logos multiple times for seamless scroll -->
          {% for i in "123456789012345678901234" %}
          <div class="logo">NETFYRE</div>
          <div class="logo">CREATIVE</div>
          <div class="logo">DIGITAL</div>
          <div class="logo">MARKETING</div>
          <div class="logo">AGENCY</div>
          <div class="logo">STUDIO</div>
          {% endfor %}
        </div>
      </div>
    </div>
  </section>

  <!-- Back to Home Button -->
  <section class="back-home">
    <div class="container">
      <a href="{% url 'netfyre_app:home' %}" class="back-btn">← Back to Home</a>
    </div>
  </section>
</div>
{% endblock %}
