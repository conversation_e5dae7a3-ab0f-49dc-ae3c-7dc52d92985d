# Generated by Django 5.2.1 on 2025-06-20 07:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0007_remove_contactsubmission_message_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='casestudy',
            options={'ordering': ['-is_featured', 'order', '-created_at'], 'verbose_name': 'Case Study', 'verbose_name_plural': 'Case Studies'},
        ),
        migrations.AddField(
            model_name='casestudy',
            name='client_name',
            field=models.CharField(default='Default Client', help_text='Client/company name', max_length=100),
        ),
        migrations.AddField(
            model_name='casestudy',
            name='excerpt',
            field=models.TextField(default='Default excerpt for this case study.', help_text='Short description for case study listing', max_length=300),
        ),
        migrations.AddField(
            model_name='casestudy',
            name='is_featured',
            field=models.BooleanField(default=False, help_text='Feature this case study'),
        ),
        migrations.AddField(
            model_name='casestudy',
            name='order',
            field=models.PositiveIntegerField(default=0, help_text='Order for display'),
        ),
        migrations.AddField(
            model_name='casestudy',
            name='project_category',
            field=models.CharField(default='Web Design', help_text='Project category (e.g., Web Design, Branding, etc.)', max_length=100),
        ),
        migrations.AddField(
            model_name='casestudy',
            name='slug',
            field=models.SlugField(blank=True, help_text='URL slug for this case study', max_length=200, unique=True),
        ),
        migrations.AlterField(
            model_name='casestudy',
            name='description',
            field=models.TextField(help_text='Main description text'),
        ),
        migrations.AlterField(
            model_name='casestudy',
            name='title',
            field=models.CharField(default='Case Study', help_text='Case study title', max_length=200),
        ),
    ]
