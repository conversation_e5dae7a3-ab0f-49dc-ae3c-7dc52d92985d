{% extends 'base.html' %}
{% load static %}

{% block title %}{{ terms_settings.page_title }} - {{ site_settings.site_title }}{% endblock %}

{% block extra_css %}
<style>
  /* Terms and Conditions Page Styles */
  .terms-conditions-page {
    background: #ffffff;
    min-height: 100vh;
    padding-top: 100px;
  }

  .terms-header {
    text-align: center;
    padding: 80px 20px 60px 20px;
    background: #ffffff;
  }

  .terms-logo {
    margin-bottom: 40px;
  }

  .terms-logo .logo-img {
    height: 60px;
    width: auto;
    max-width: 200px;
    object-fit: contain;
  }

  .terms-header h1 {
    font-size: 3.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 20px;
    line-height: 1.2;
  }

  .terms-header .subtitle {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto 20px auto;
    line-height: 1.6;
  }

  .effective-date {
    font-size: 1rem;
    color: #888;
    font-style: italic;
    margin-top: 10px;
  }

  .terms-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px 80px 20px;
  }

  .terms-section {
    margin-bottom: 50px;
  }

  .terms-section h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 20px;
    line-height: 1.3;
  }

  .terms-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    margin-top: 30px;
    line-height: 1.4;
  }

  .terms-section p {
    font-size: 1rem;
    color: #555;
    line-height: 1.7;
    margin-bottom: 20px;
  }

  .terms-section ul {
    margin: 20px 0;
    padding-left: 30px;
  }

  .terms-section li {
    font-size: 1rem;
    color: #555;
    line-height: 1.7;
    margin-bottom: 10px;
  }

  .terms-section ol {
    margin: 20px 0;
    padding-left: 30px;
  }

  .terms-section ol li {
    font-size: 1rem;
    color: #555;
    line-height: 1.7;
    margin-bottom: 15px;
  }

  .contact-info {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    margin: 40px 0;
    text-align: center;
  }

  .contact-info h3 {
    color: #1a1a1a;
    margin-bottom: 15px;
  }

  .contact-info p {
    margin-bottom: 10px;
  }

  .contact-info a {
    color: #ffb600;
    text-decoration: none;
    font-weight: 500;
  }

  .contact-info a:hover {
    text-decoration: underline;
  }

  .last-updated {
    text-align: center;
    font-size: 0.9rem;
    color: #888;
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
  }

  .important-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 20px;
    margin: 30px 0;
  }

  .important-notice h4 {
    color: #856404;
    margin-bottom: 10px;
    font-weight: 600;
  }

  .important-notice p {
    color: #856404;
    margin-bottom: 0;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .terms-header {
      padding: 60px 15px 40px 15px;
    }

    .terms-header h1 {
      font-size: 2.5rem;
    }

    .terms-header .subtitle {
      font-size: 1rem;
    }

    .terms-content {
      padding: 0 15px 60px 15px;
    }

    .terms-section h2 {
      font-size: 1.7rem;
    }

    .terms-section h3 {
      font-size: 1.3rem;
    }

    .contact-info {
      padding: 20px;
    }
  }

  @media (max-width: 480px) {
    .terms-header h1 {
      font-size: 2rem;
    }

    .terms-section h2 {
      font-size: 1.5rem;
    }

    .terms-section h3 {
      font-size: 1.2rem;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="terms-conditions-page">
  <!-- Terms and Conditions Header -->
  <section class="terms-header">
    <!-- NetFyre Logo -->
    <div class="terms-logo">
      {% if site_settings.logo %}
        <img src="{{ site_settings.logo.url }}" alt="{{ site_settings.site_title }}" class="logo-img" />
      {% else %}
        <img src="{% static 'images/buttonlogo.png' %}" alt="NetFyre" class="logo-img" />
      {% endif %}
    </div>
    
    <h1>{{ terms_settings.page_title }}</h1>
    <p class="subtitle">
      {{ terms_settings.subtitle }}
    </p>
    {% if terms_settings.effective_date %}
    <p class="effective-date">
      Effective Date: {{ terms_settings.effective_date|date:"F d, Y" }}
    </p>
    {% endif %}
  </section>

  <!-- Terms and Conditions Content -->
  <div class="terms-content">
    {% for section in terms_sections %}
    <div class="terms-section">
      <h2>{{ section.title }}</h2>
      <div class="section-content">
        {{ section.content|safe }}
      </div>
    </div>
    {% empty %}
    <!-- Default content if no sections are created -->
    <div class="terms-section">
      <h2>Acceptance of Terms</h2>
      <p>
        By accessing and using this website, you accept and agree to be bound by the terms and provision of this agreement.
        If you do not agree to abide by the above, please do not use this service.
      </p>
    </div>

    <div class="terms-section">
      <h2>Use License</h2>
      <p>
        Permission is granted to temporarily download one copy of the materials on NetFyre's website for personal, 
        non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:
      </p>
      <ul>
        <li>modify or copy the materials</li>
        <li>use the materials for any commercial purpose or for any public display (commercial or non-commercial)</li>
        <li>attempt to decompile or reverse engineer any software contained on the website</li>
        <li>remove any copyright or other proprietary notations from the materials</li>
      </ul>
    </div>

    <div class="terms-section">
      <h2>Disclaimer</h2>
      <p>
        The materials on NetFyre's website are provided on an 'as is' basis. NetFyre makes no warranties, 
        expressed or implied, and hereby disclaims and negates all other warranties including without limitation, 
        implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement 
        of intellectual property or other violation of rights.
      </p>
    </div>

    <div class="important-notice">
      <h4>Important Notice</h4>
      <p>
        These terms and conditions are subject to change without notice. Please review them periodically for updates.
      </p>
    </div>
    {% endfor %}

    <!-- Contact Information -->
    <div class="contact-info">
      <h3>{{ terms_settings.contact_title }}</h3>
      <p>{{ terms_settings.contact_description }}</p>
      <p>Email: <a href="mailto:{{ terms_settings.contact_email }}">{{ terms_settings.contact_email }}</a></p>
      <p>Phone: <a href="tel:{{ terms_settings.contact_phone|cut:' '|cut:'('|cut:')'|cut:'-' }}">{{ terms_settings.contact_phone }}</a></p>
      <p>Address: {{ terms_settings.contact_address }}</p>
    </div>

    <div class="last-updated">
      <p>Last updated: {{ terms_settings.last_updated|date:"F d, Y" }}</p>
    </div>
  </div>
</div>
{% endblock %}
