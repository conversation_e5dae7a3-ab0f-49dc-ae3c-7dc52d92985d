# Generated by Django 5.2.1 on 2025-06-21 06:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0013_cookieconsent_cookieactivity'),
    ]

    operations = [
        migrations.CreateModel(
            name='TermsAndConditionsSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Section title', max_length=200)),
                ('content', models.TextField(help_text='Section content (supports HTML)')),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Terms and Conditions Section',
                'verbose_name_plural': 'Terms and Conditions Sections',
                'ordering': ['order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='TermsAndConditionsSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('page_title', models.CharField(default='Terms and Conditions', help_text='Main page title', max_length=200)),
                ('subtitle', models.TextField(default='Please read these terms and conditions carefully before using our services. By accessing or using our website, you agree to be bound by these terms.', help_text='Page subtitle/description')),
                ('effective_date', models.DateField(help_text='When these terms became effective')),
                ('contact_title', models.CharField(default='Contact Us', help_text='Contact section title', max_length=200)),
                ('contact_description', models.TextField(default='If you have any questions about these Terms and Conditions, please contact us:', help_text='Contact section description')),
                ('contact_email', models.EmailField(default='<EMAIL>', help_text='Legal contact email', max_length=254)),
                ('contact_phone', models.CharField(default='+****************', help_text='Legal contact phone', max_length=20)),
                ('contact_address', models.TextField(default='123 Business Street, City, State 12345', help_text='Legal contact address')),
                ('last_updated', models.DateTimeField(auto_now=True, help_text='Last updated timestamp')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Terms and Conditions Settings',
                'verbose_name_plural': 'Terms and Conditions Settings',
            },
        ),
    ]
