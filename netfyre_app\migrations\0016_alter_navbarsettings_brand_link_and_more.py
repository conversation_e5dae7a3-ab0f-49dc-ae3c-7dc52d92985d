# Generated by Django 5.2.1 on 2025-06-21 06:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0015_navbarsettings_navbarsociallink_navbarlink'),
    ]

    operations = [
        migrations.AlterField(
            model_name='navbarsettings',
            name='brand_link',
            field=models.CharField(default='/', help_text="Where logo/brand links to (can be relative like '/' or absolute)", max_length=200),
        ),
        migrations.AlterField(
            model_name='navbarsettings',
            name='cta_button_link',
            field=models.CharField(default='/contact/', help_text="CTA button link (can be relative like '/contact/' or absolute)", max_length=200),
        ),
    ]
