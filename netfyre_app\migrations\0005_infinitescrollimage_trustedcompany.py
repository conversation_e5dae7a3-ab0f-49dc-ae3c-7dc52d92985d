# Generated by Django 5.2.1 on 2025-06-19 18:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0004_casestudy'),
    ]

    operations = [
        migrations.CreateModel(
            name='InfiniteScrollImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Image title/description', max_length=200)),
                ('image', models.ImageField(help_text='Upload image for infinite scroll', upload_to='infinite_scroll/')),
                ('alt_text', models.CharField(blank=True, help_text='Alt text for accessibility', max_length=200)),
                ('order', models.PositiveIntegerField(default=0, help_text='Order of display in the scroll')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Infinite Scroll Image',
                'verbose_name_plural': 'Infinite Scroll Images',
                'ordering': ['order', 'title'],
            },
        ),
        migrations.CreateModel(
            name='TrustedCompany',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(help_text='Company name', max_length=100)),
                ('logo_image', models.ImageField(blank=True, help_text='Company logo image (optional)', null=True, upload_to='trusted_companies/')),
                ('website_url', models.URLField(blank=True, help_text='Company website URL')),
                ('order', models.PositiveIntegerField(default=0, help_text='Order of display in the scroll')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Trusted Company',
                'verbose_name_plural': 'Trusted Companies',
                'ordering': ['order', 'company_name'],
            },
        ),
    ]
