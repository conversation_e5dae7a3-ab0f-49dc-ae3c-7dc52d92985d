# Generated by Django 5.2.1 on 2025-06-20 01:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0005_infinitescrollimage_trustedcompany'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContactSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fullname', models.CharField(help_text='Full name of the contact', max_length=100)),
                ('email', models.EmailField(help_text='Email address', max_length=254)),
                ('phone', models.CharField(help_text='Phone number', max_length=20)),
                ('website', models.URLField(help_text='Website URL')),
                ('revenue', models.CharField(choices=[('0-10k', '$0 - $10,000'), ('10k-50k', '$10,000 - $50,000'), ('50k-100k', '$50,000 - $100,000'), ('100k+', '$100,000+')], help_text='Monthly revenue range', max_length=20)),
                ('source', models.CharField(choices=[('google', 'Google Search'), ('social', 'Social Media'), ('referral', 'Referral'), ('other', 'Other')], help_text='How they heard about us', max_length=20)),
                ('message', models.TextField(help_text='Project description and requirements')),
                ('submitted_at', models.DateTimeField(auto_now_add=True)),
                ('is_read', models.BooleanField(default=False, help_text='Mark as read')),
            ],
            options={
                'verbose_name': 'Contact Submission',
                'verbose_name_plural': 'Contact Submissions',
                'ordering': ['-submitted_at'],
            },
        ),
    ]
