from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.contrib import messages
from django.views.decorators.csrf import csrf_exempt
from .models import (
    Service, Testimonial, Project, CompanyLogo,
    FAQ, SiteSettings, Counter, GalleryImage, AboutSection, CaseStudy,
    InfiniteScrollImage, TrustedCompany, TeamMember, TeamMemberPortfolioImage,
    TeamMemberPortfolioVideo, TeamMemberProject, ContactSubmission,
    PrivacyPolicySection, PrivacyPolicySettings, CookieConsent, CookieActivity,
    TermsAndConditionsSection, TermsAndConditionsSettings
)


def home(request):
    """
    Home page view that displays all the main content sections
    """
    # Get site settings (create default if doesn't exist)
    try:
        site_settings = SiteSettings.objects.first()
    except SiteSettings.DoesNotExist:
        site_settings = SiteSettings.objects.create()

    # Get about section settings (create default if doesn't exist)
    try:
        about_section = AboutSection.objects.first()
    except AboutSection.DoesNotExist:
        about_section = AboutSection.objects.create()
    
    # Get active content for each section
    services = Service.objects.filter(is_active=True)[:6]  # Limit to 6 for display
    testimonials = Testimonial.objects.filter(is_active=True)[:10]  # Limit for carousel
    projects = Project.objects.filter(is_active=True)[:12]  # Limit for gallery
    featured_projects = Project.objects.filter(is_featured=True, is_active=True)[:6]

    # Create seamless loop for hero gallery by repeating images multiple times
    hero_gallery_images = list(Project.objects.filter(is_active=True)[:6])
    if hero_gallery_images:
        # Repeat images many times for perfect seamless infinite scroll (no visible first/last)
        hero_gallery_column_1 = hero_gallery_images * 12  # Column 1 (scrolling up)
        hero_gallery_column_2 = hero_gallery_images * 12  # Column 2 (scrolling down)
        hero_gallery_column_3 = hero_gallery_images * 12  # Column 3 (scrolling up)
    else:
        hero_gallery_column_1 = []
        hero_gallery_column_2 = []
        hero_gallery_column_3 = []
    company_logos = CompanyLogo.objects.filter(is_active=True)[:8]
    faqs = FAQ.objects.filter(is_active=True)[:5]  # Limit to 5 main FAQs
    counters = Counter.objects.filter(is_active=True)[:3]  # Typically 3 counters

    # Get gallery images organized by column and repeat them for seamless loop
    gallery_column_1_base = list(GalleryImage.objects.filter(is_active=True, column=1))
    gallery_column_2_base = list(GalleryImage.objects.filter(is_active=True, column=2))
    gallery_column_3_base = list(GalleryImage.objects.filter(is_active=True, column=3))

    # Repeat images multiple times for seamless infinite scroll
    gallery_column_1 = gallery_column_1_base * 6 if gallery_column_1_base else []
    gallery_column_2 = gallery_column_2_base * 6 if gallery_column_2_base else []
    gallery_column_3 = gallery_column_3_base * 6 if gallery_column_3_base else []
    
    context = {
        'site_settings': site_settings,
        'about_section': about_section,
        'services': services,
        'testimonials': testimonials,
        'projects': projects,
        'featured_projects': featured_projects,
        'company_logos': company_logos,
        'faqs': faqs,
        'counters': counters,
        'gallery_column_1': gallery_column_1,
        'gallery_column_2': gallery_column_2,
        'gallery_column_3': gallery_column_3,
        'hero_gallery_column_1': hero_gallery_column_1,
        'hero_gallery_column_2': hero_gallery_column_2,
        'hero_gallery_column_3': hero_gallery_column_3,
    }
    
    return render(request, 'netfyre_app/home.html', context)


def services_list(request):
    """
    View to display all services
    """
    services = Service.objects.filter(is_active=True)
    
    # Filter by category if provided
    category = request.GET.get('category')
    if category:
        services = services.filter(category__icontains=category)
    
    # Pagination
    paginator = Paginator(services, 12)  # Show 12 services per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get all categories for filter
    categories = Service.objects.filter(is_active=True).values_list('category', flat=True).distinct()
    
    context = {
        'services': page_obj,
        'categories': categories,
        'current_category': category,
    }
    
    return render(request, 'netfyre_app/services.html', context)


def service_detail(request, pk):
    """
    View to display individual service details
    """
    service = get_object_or_404(Service, pk=pk, is_active=True)
    related_services = Service.objects.filter(
        category=service.category, 
        is_active=True
    ).exclude(pk=pk)[:3]
    
    context = {
        'service': service,
        'related_services': related_services,
    }
    
    return render(request, 'netfyre_app/service_detail.html', context)


def projects_list(request):
    """
    View to display all projects
    """
    projects = Project.objects.filter(is_active=True)
    
    # Filter by featured if requested
    if request.GET.get('featured') == 'true':
        projects = projects.filter(is_featured=True)
    
    # Pagination
    paginator = Paginator(projects, 12)  # Show 12 projects per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'projects': page_obj,
    }
    
    return render(request, 'netfyre_app/projects.html', context)


def project_detail(request, pk):
    """
    View to display individual project details
    """
    project = get_object_or_404(Project, pk=pk, is_active=True)
    related_projects = Project.objects.filter(is_active=True).exclude(pk=pk)[:3]
    
    context = {
        'project': project,
        'related_projects': related_projects,
    }
    
    return render(request, 'netfyre_app/project_detail.html', context)


def testimonials_list(request):
    """
    View to display all testimonials
    """
    testimonials = Testimonial.objects.filter(is_active=True)
    
    # Pagination
    paginator = Paginator(testimonials, 10)  # Show 10 testimonials per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'testimonials': page_obj,
    }
    
    return render(request, 'netfyre_app/testimonials.html', context)


def about(request):
    """
    About page view
    """
    try:
        site_settings = SiteSettings.objects.first()
    except SiteSettings.DoesNotExist:
        site_settings = SiteSettings.objects.create()

    counters = Counter.objects.filter(is_active=True)
    featured_projects = Project.objects.filter(is_featured=True, is_active=True)[:6]

    context = {
        'site_settings': site_settings,
        'counters': counters,
        'featured_projects': featured_projects,
    }

    return render(request, 'netfyre_app/about.html', context)


def team(request):
    """
    Team page view to showcase all team members
    """
    try:
        site_settings = SiteSettings.objects.first()
    except SiteSettings.DoesNotExist:
        site_settings = SiteSettings.objects.create()

    # Get all active team members
    team_members = TeamMember.objects.filter(is_active=True)

    # Get featured team members for hero section
    featured_members = team_members.filter(is_featured=True)[:3]

    # Get team members by role for organized display
    founders = team_members.filter(role__in=['founder', 'ceo'])
    designers = team_members.filter(role='designer')
    developers = team_members.filter(role='developer')
    marketers = team_members.filter(role='marketer')
    strategists = team_members.filter(role='strategist')
    managers = team_members.filter(role='manager')
    others = team_members.filter(role__in=['analyst', 'copywriter', 'videographer', 'other'])

    # Get some testimonials and projects for additional content
    testimonials = Testimonial.objects.filter(is_active=True)[:5]
    featured_projects = Project.objects.filter(is_featured=True, is_active=True)[:6]

    context = {
        'site_settings': site_settings,
        'team_members': team_members,
        'featured_members': featured_members,
        'founders': founders,
        'designers': designers,
        'developers': developers,
        'marketers': marketers,
        'strategists': strategists,
        'managers': managers,
        'others': others,
        'testimonials': testimonials,
        'featured_projects': featured_projects,
    }

    return render(request, 'netfyre_app/team.html', context)


def team_member_detail(request, pk):
    """
    Individual team member detail page with portfolio showcase
    """
    try:
        site_settings = SiteSettings.objects.first()
    except SiteSettings.DoesNotExist:
        site_settings = SiteSettings.objects.create()

    # Get the specific team member
    team_member = get_object_or_404(TeamMember, pk=pk, is_active=True)

    # Get portfolio images
    portfolio_images = TeamMemberPortfolioImage.objects.filter(
        team_member=team_member,
        is_active=True
    ).order_by('order', '-created_at')

    # Get portfolio videos
    portfolio_videos = TeamMemberPortfolioVideo.objects.filter(
        team_member=team_member,
        is_active=True
    ).order_by('order', '-created_at')

    # Get projects
    projects = TeamMemberProject.objects.filter(
        team_member=team_member,
        is_active=True
    ).order_by('order', '-completion_date')

    # Get featured items
    featured_images = portfolio_images.filter(is_featured=True)[:6]
    featured_videos = portfolio_videos.filter(is_featured=True)[:3]
    featured_projects = projects.filter(is_featured=True)[:4]

    # Get categories for filtering
    image_categories = portfolio_images.values_list('category', flat=True).distinct()
    video_categories = portfolio_videos.values_list('category', flat=True).distinct()

    # Get related team members
    related_members = TeamMember.objects.filter(
        is_active=True,
        role=team_member.role
    ).exclude(pk=team_member.pk)[:3]

    context = {
        'site_settings': site_settings,
        'team_member': team_member,
        'portfolio_images': portfolio_images,
        'portfolio_videos': portfolio_videos,
        'projects': projects,
        'featured_images': featured_images,
        'featured_videos': featured_videos,
        'featured_projects': featured_projects,
        'image_categories': image_categories,
        'video_categories': video_categories,
        'related_members': related_members,
    }

    return render(request, 'netfyre_app/team_member_detail.html', context)





def casestudies_list(request):
    """
    Case Studies listing page view
    """
    try:
        site_settings = SiteSettings.objects.first()
    except SiteSettings.DoesNotExist:
        site_settings = SiteSettings.objects.create()

    # Get all active case studies
    case_studies = CaseStudy.objects.filter(is_active=True)

    context = {
        'site_settings': site_settings,
        'case_studies': case_studies,
    }

    return render(request, 'netfyre_app/casestudies_list.html', context)


def casestudy_detail(request, slug):
    """
    Individual Case Study detail page view
    """
    try:
        site_settings = SiteSettings.objects.first()
    except SiteSettings.DoesNotExist:
        site_settings = SiteSettings.objects.create()

    # Get the specific case study
    case_study = get_object_or_404(CaseStudy, slug=slug, is_active=True)

    # Get infinite scroll images and trusted companies for the moving sections
    infinite_scroll_images = InfiniteScrollImage.objects.filter(is_active=True).order_by('order')
    trusted_companies = TrustedCompany.objects.filter(is_active=True).order_by('order')

    # Fallback to projects and company logos if new models are empty
    projects = Project.objects.filter(is_active=True)[:12] if not infinite_scroll_images.exists() else None
    company_logos = CompanyLogo.objects.filter(is_active=True)[:8] if not trusted_companies.exists() else None

    # Get related case studies (exclude current one)
    related_case_studies = CaseStudy.objects.filter(is_active=True).exclude(id=case_study.id)[:3]

    context = {
        'site_settings': site_settings,
        'case_study': case_study,
        'infinite_scroll_images': infinite_scroll_images,
        'trusted_companies': trusted_companies,
        'projects': projects,
        'company_logos': company_logos,
        'related_case_studies': related_case_studies,
    }

    return render(request, 'netfyre_app/casestudy_detail.html', context)


def casestudy_redirect(request):
    """
    Redirect old casestudy URL to new casestudies list
    """
    from django.shortcuts import redirect
    return redirect('netfyre_app:casestudies_list')


def api_services(request):
    """
    API endpoint to get services data (for AJAX requests)
    """
    services = Service.objects.filter(is_active=True).values(
        'id', 'title', 'subtitle', 'icon', 'category', 'description'
    )
    
    return JsonResponse({
        'services': list(services)
    })


def api_testimonials(request):
    """
    API endpoint to get testimonials data (for AJAX requests)
    """
    testimonials = Testimonial.objects.filter(is_active=True).values(
        'id', 'client_name', 'client_title', 'client_company', 
        'testimonial_text', 'rating'
    )
    
    return JsonResponse({
        'testimonials': list(testimonials)
    })


def api_projects(request):
    """
    API endpoint to get projects data (for AJAX requests)
    """
    projects = Project.objects.filter(is_active=True).values(
        'id', 'title', 'description', 'client', 'technologies'
    )
    
    return JsonResponse({
        'projects': list(projects)
    })


def contact(request):
    """
    Contact page view with form handling
    """
    site_settings = SiteSettings.objects.first()

    if request.method == 'POST':
        try:
            # Create contact submission
            contact_submission = ContactSubmission.objects.create(
                fullname=request.POST.get('fullname'),
                email=request.POST.get('email'),
                phone=request.POST.get('phone'),
                website=request.POST.get('website'),
                revenue=request.POST.get('revenue'),
                source=request.POST.get('source'),
                business_type=request.POST.get('business_type')
            )

            # Send success message
            messages.success(request, 'Thank you for your message! We\'ll get back to you soon.')

            # Optional: Send email notification to admin
            # You can implement email sending here

            return redirect('netfyre_app:contact')

        except Exception as e:
            messages.error(request, 'There was an error submitting your form. Please try again.')

    context = {
        'site_settings': site_settings,
    }

    return render(request, 'netfyre_app/contact.html', context)


def privacy_policy(request):
    """
    Privacy Policy page view
    """
    try:
        site_settings = SiteSettings.objects.first()
    except SiteSettings.DoesNotExist:
        site_settings = SiteSettings.objects.create()

    # Get privacy policy settings
    try:
        privacy_settings = PrivacyPolicySettings.objects.first()
    except PrivacyPolicySettings.DoesNotExist:
        privacy_settings = PrivacyPolicySettings.objects.create()

    # Get all active privacy policy sections
    privacy_sections = PrivacyPolicySection.objects.filter(is_active=True).order_by('order', 'created_at')

    context = {
        'site_settings': site_settings,
        'privacy_settings': privacy_settings,
        'privacy_sections': privacy_sections,
    }

    return render(request, 'netfyre_app/privacy_policy.html', context)


@csrf_exempt
def cookie_consent(request):
    """
    Handle cookie consent data from frontend
    """
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)

            # Get user information
            session_key = request.session.session_key
            if not session_key:
                request.session.create()
                session_key = request.session.session_key

            # Get or create IP address
            def get_client_ip(request):
                x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
                if x_forwarded_for:
                    ip = x_forwarded_for.split(',')[0]
                else:
                    ip = request.META.get('REMOTE_ADDR')
                return ip

            ip_address = get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            page_url = data.get('page_url', '')
            referrer = request.META.get('HTTP_REFERER', '')

            # Get or create consent record
            consent, created = CookieConsent.objects.get_or_create(
                session_key=session_key,
                defaults={
                    'ip_address': ip_address,
                    'user_agent': user_agent,
                    'page_url': page_url,
                    'referrer': referrer,
                }
            )

            # Update consent based on action
            action = data.get('action')
            settings = data.get('settings', {})

            if action == 'accept_all':
                consent.consent_given = True
                consent.consent_declined = False
                consent.necessary_cookies = True
                consent.analytics_cookies = True
                consent.marketing_cookies = True
                consent.preferences_cookies = True

                # Log activity
                CookieActivity.objects.create(
                    consent=consent,
                    action='accepted_all',
                    page_url=page_url,
                    user_agent=user_agent,
                    settings_snapshot=settings
                )

            elif action == 'decline_all':
                consent.consent_given = False
                consent.consent_declined = True
                consent.necessary_cookies = True
                consent.analytics_cookies = False
                consent.marketing_cookies = False
                consent.preferences_cookies = False

                # Log activity
                CookieActivity.objects.create(
                    consent=consent,
                    action='declined_all',
                    page_url=page_url,
                    user_agent=user_agent,
                    settings_snapshot=settings
                )

            elif action == 'save_settings':
                consent.consent_given = True
                consent.consent_declined = False
                consent.necessary_cookies = True  # Always true
                consent.analytics_cookies = settings.get('analytics', False)
                consent.marketing_cookies = settings.get('marketing', False)
                consent.preferences_cookies = settings.get('preferences', False)

                # Log activity
                CookieActivity.objects.create(
                    consent=consent,
                    action='settings_saved',
                    page_url=page_url,
                    user_agent=user_agent,
                    settings_snapshot=settings
                )

            elif action == 'banner_shown':
                # Log banner display
                CookieActivity.objects.create(
                    consent=consent,
                    action='banner_shown',
                    page_url=page_url,
                    user_agent=user_agent,
                    settings_snapshot=settings
                )

            elif action == 'settings_opened':
                # Log settings modal opened
                CookieActivity.objects.create(
                    consent=consent,
                    action='settings_opened',
                    page_url=page_url,
                    user_agent=user_agent,
                    settings_snapshot=settings
                )

            # Save consent record
            consent.save()

            return JsonResponse({
                'status': 'success',
                'message': 'Cookie consent saved successfully',
                'consent_id': consent.id
            })

        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=400)

    return JsonResponse({
        'status': 'error',
        'message': 'Only POST method allowed'
    }, status=405)


def terms_conditions(request):
    """
    Terms and Conditions page view
    """
    try:
        site_settings = SiteSettings.objects.first()
    except SiteSettings.DoesNotExist:
        site_settings = SiteSettings.objects.create()

    # Get terms and conditions settings
    try:
        terms_settings = TermsAndConditionsSettings.objects.first()
    except TermsAndConditionsSettings.DoesNotExist:
        terms_settings = TermsAndConditionsSettings.objects.create()

    # Get all active terms and conditions sections
    terms_sections = TermsAndConditionsSection.objects.filter(is_active=True).order_by('order', 'created_at')

    context = {
        'site_settings': site_settings,
        'terms_settings': terms_settings,
        'terms_sections': terms_sections,
    }

    return render(request, 'netfyre_app/terms_conditions.html', context)
