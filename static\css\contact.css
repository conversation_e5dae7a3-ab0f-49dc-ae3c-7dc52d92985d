/* Contact Page Styles */
.contact-container {
  display: flex;
  min-height: fit-content;
  max-width: fit-content;
  height: 900px;
  margin: 0 auto;
  background-color: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.062);
  margin-top: 30px; /* Account for fixed navbar */
}

.left-section {
  flex: 1;
  padding: 80px 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #fcfcfc;
  height: fit-content;
}

.left-section h1 {
  font-size: 3.3rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 30px;
  line-height: 1.1;
}

.subtitle {
  font-size: 1.1rem;
  line-height: 1.5;
  font-weight: 650;
  margin-bottom: 10px;
}

.book-call-btn {
  background-color: transparent;
  border: 2px solid #1a1a1a;
  color: #1a1a1a;
  padding: 15px 30px;
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 1px;
  cursor: pointer;
  border-radius: 50px;
  transition: all 0.3s ease;
  align-self: flex-start;
  margin-top: 20px;
}

.book-call-btn:hover {
  background-color: #1a1a1a;
  color: white;
  transform: translateY(-2px);
}

.right-section {
  flex: 1;
  padding: 80px 60px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  height: fit-content;
}

.contact-form {
  width: 100%;
  max-width: 500px;
}

/* Messages */
.messages {
  margin-bottom: 20px;
}

.alert {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 10px;
  font-weight: 500;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}

.form-group {
  margin-bottom: 30px;
  flex: 1;
}

.form-group-full {
  margin-bottom: 30px;
  width: 100%;
}

.form-group label,
.form-group-full label {
  display: block;
  font-size: 0.8rem;
  font-weight: 650;
  color: #1a1a1a;
  margin-bottom: 8px;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.required {
  color: #e74c3c;
}

.form-group input,
.form-group textarea,
.form-group select,
.form-group-full input,
.form-group-full textarea,
.form-group-full select {
  width: 100%;
  padding: 15px 12px;
  background-color: transparent;
  font-size: 1rem;
  color: #333;
  border-radius: 10px;
  border: 1px solid #d8d8d8;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus,
.form-group-full input:focus,
.form-group-full textarea:focus,
.form-group-full select:focus {
  border-color: #1a1a1a;
  outline: none;
}

.form-group input::placeholder,
.form-group textarea::placeholder,
.form-group-full input::placeholder,
.form-group-full textarea::placeholder {
  color: #999;
}

.form-group textarea,
.form-group-full textarea {
  resize: vertical;
  min-height: 100px;
}

.form-group select,
.form-group-full select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 40px;
}

.submit-btn {
  width: 100%;
  background-color: #1a1a1a;
  color: white;
  border: none;
  padding: 18px;
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 1px;
  cursor: pointer;
  border-radius: 50px;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.submit-btn:hover {
  background-color: #333;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-container {
    flex-direction: column;
    min-height: fit-content;
    margin-top: 80px;
  }

  .left-section,
  .right-section {
    padding: 40px 30px;
  }

  .left-section h1 {
    font-size: 2.5rem;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }
}

@media (max-width: 480px) {
  .left-section,
  .right-section {
    padding: 30px 20px;
  }

  .left-section h1 {
    font-size: 2rem;
  }
}
