/**
 * Cookies Consent Banner JavaScript
 * Professional implementation with localStorage persistence
 */

class CookiesManager {
  constructor() {
    this.cookiesAccepted = false;
    this.cookiesDeclined = false;
    this.cookieSettings = {
      necessary: true, // Always true, cannot be disabled
      analytics: false,
      marketing: false,
      preferences: false,
    };

    this.init();
  }

  init() {
    console.log("CookiesManager initialized");

    // Check if user has already made a choice
    const savedConsent = localStorage.getItem("cookieConsent");
    const savedSettings = localStorage.getItem("cookieSettings");

    console.log("Saved consent:", savedConsent);
    console.log("Saved settings:", savedSettings);

    if (savedConsent) {
      const consent = JSON.parse(savedConsent);
      this.cookiesAccepted = consent.accepted;
      this.cookiesDeclined = consent.declined;

      if (savedSettings) {
        this.cookieSettings = {
          ...this.cookieSettings,
          ...JSON.parse(savedSettings),
        };
      }

      // If user has made a choice, don't show banner
      if (this.cookiesAccepted || this.cookiesDeclined) {
        console.log("User has already made a choice, loading cookies");
        this.loadCookies();
        return;
      }
    }

    // Show banner after a short delay
    console.log("Showing banner in 1 second");
    setTimeout(() => {
      this.showBanner();
    }, 1000);
  }

  showBanner() {
    console.log("Attempting to show banner");
    const banner = document.getElementById("cookiesBanner");
    console.log("Banner element found:", banner);

    if (banner) {
      console.log("Adding 'show' class to banner");
      banner.classList.add("show");
      // Track banner display
      setTimeout(() => {
        this.trackBannerShown();
      }, 500);
    } else {
      console.error("Cookie banner element not found!");
    }
  }

  hideBanner() {
    const banner = document.getElementById("cookiesBanner");
    if (banner) {
      banner.classList.remove("show");
      banner.classList.add("hide");

      // Remove from DOM after animation
      setTimeout(() => {
        banner.style.display = "none";
      }, 400);
    }
  }

  acceptCookies() {
    this.cookiesAccepted = true;
    this.cookiesDeclined = false;

    // Enable all cookie types when accepting
    this.cookieSettings = {
      necessary: true,
      analytics: true,
      marketing: true,
      preferences: true,
    };

    this.saveConsent();
    this.sendToBackend("accept_all", this.cookieSettings);
    this.loadCookies();
    this.hideBanner();
  }

  declineCookies() {
    this.cookiesAccepted = false;
    this.cookiesDeclined = true;

    // Only keep necessary cookies
    this.cookieSettings = {
      necessary: true,
      analytics: false,
      marketing: false,
      preferences: false,
    };

    this.saveConsent();
    this.sendToBackend("decline_all", this.cookieSettings);
    this.hideBanner();
  }

  saveConsent() {
    const consent = {
      accepted: this.cookiesAccepted,
      declined: this.cookiesDeclined,
      timestamp: new Date().toISOString(),
    };

    localStorage.setItem("cookieConsent", JSON.stringify(consent));
    localStorage.setItem("cookieSettings", JSON.stringify(this.cookieSettings));
  }

  loadCookies() {
    if (this.cookieSettings.analytics) {
      this.loadAnalytics();
    }

    if (this.cookieSettings.marketing) {
      this.loadMarketing();
    }

    if (this.cookieSettings.preferences) {
      this.loadPreferences();
    }
  }

  loadAnalytics() {
    // Load Google Analytics or other analytics scripts
    console.log("Loading analytics cookies...");

    // Example: Google Analytics
    // gtag('config', 'GA_MEASUREMENT_ID');
  }

  loadMarketing() {
    // Load marketing/advertising scripts
    console.log("Loading marketing cookies...");

    // Example: Facebook Pixel, Google Ads, etc.
  }

  loadPreferences() {
    // Load preference cookies
    console.log("Loading preference cookies...");

    // Example: Theme preferences, language settings, etc.
  }

  showSettings() {
    const modal = document.getElementById("cookiesModal");
    if (modal) {
      modal.classList.add("show");

      // Update toggle states
      this.updateSettingsToggles();

      // Track settings modal opened
      this.trackSettingsOpened();
    }
  }

  hideSettings() {
    const modal = document.getElementById("cookiesModal");
    if (modal) {
      modal.classList.remove("show");
    }
  }

  updateSettingsToggles() {
    // Update toggle switches based on current settings
    Object.keys(this.cookieSettings).forEach((category) => {
      const toggle = document.getElementById(`toggle-${category}`);
      if (toggle) {
        toggle.checked = this.cookieSettings[category];

        // Disable necessary cookies toggle
        if (category === "necessary") {
          toggle.disabled = true;
        }
      }
    });
  }

  saveSettings() {
    // Get current toggle states
    Object.keys(this.cookieSettings).forEach((category) => {
      if (category !== "necessary") {
        // Necessary cookies always enabled
        const toggle = document.getElementById(`toggle-${category}`);
        if (toggle) {
          this.cookieSettings[category] = toggle.checked;
        }
      }
    });

    // Update consent status
    this.cookiesAccepted = true;
    this.cookiesDeclined = false;

    this.saveConsent();
    this.sendToBackend("save_settings", this.cookieSettings);
    this.loadCookies();
    this.hideSettings();
    this.hideBanner();
  }

  resetConsent() {
    // Clear all stored consent data
    localStorage.removeItem("cookieConsent");
    localStorage.removeItem("cookieSettings");

    // Reset internal state
    this.cookiesAccepted = false;
    this.cookiesDeclined = false;
    this.cookieSettings = {
      necessary: true,
      analytics: false,
      marketing: false,
      preferences: false,
    };

    // Show banner again
    this.showBanner();
  }

  sendToBackend(action, settings = {}) {
    // Send cookie consent data to Django backend
    const data = {
      action: action,
      settings: settings,
      page_url: window.location.href,
      timestamp: new Date().toISOString(),
    };

    // Get CSRF token
    const csrfToken = this.getCSRFToken();

    fetch("/api/cookie-consent/", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": csrfToken,
      },
      body: JSON.stringify(data),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.status === "success") {
          console.log("Cookie consent saved to backend:", data);
        } else {
          console.error("Failed to save cookie consent:", data.message);
        }
      })
      .catch((error) => {
        console.error("Error sending cookie consent to backend:", error);
      });
  }

  trackBannerShown() {
    // Track when banner is shown
    this.sendToBackend("banner_shown", this.cookieSettings);
  }

  trackSettingsOpened() {
    // Track when settings modal is opened
    this.sendToBackend("settings_opened", this.cookieSettings);
  }

  getCSRFToken() {
    // Get CSRF token from cookie or meta tag
    let csrfToken = null;

    // Try to get from cookie first
    const cookies = document.cookie.split(";");
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split("=");
      if (name === "csrftoken") {
        csrfToken = value;
        break;
      }
    }

    // If not found in cookie, try meta tag
    if (!csrfToken) {
      const metaTag = document.querySelector('meta[name="csrf-token"]');
      if (metaTag) {
        csrfToken = metaTag.getAttribute("content");
      }
    }

    // If still not found, try hidden input
    if (!csrfToken) {
      const hiddenInput = document.querySelector(
        'input[name="csrfmiddlewaretoken"]'
      );
      if (hiddenInput) {
        csrfToken = hiddenInput.value;
      }
    }

    return csrfToken || "";
  }
}

// Initialize cookies manager when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  window.cookiesManager = new CookiesManager();

  // Bind event listeners
  const acceptBtn = document.getElementById("acceptCookies");
  const declineBtn = document.getElementById("declineCookies");
  const settingsBtn = document.getElementById("cookieSettings");
  const modalCloseBtn = document.getElementById("closeModal");
  const saveSettingsBtn = document.getElementById("saveSettings");
  const cancelSettingsBtn = document.getElementById("cancelSettings");

  if (acceptBtn) {
    acceptBtn.addEventListener("click", () =>
      window.cookiesManager.acceptCookies()
    );
  }

  if (declineBtn) {
    declineBtn.addEventListener("click", () =>
      window.cookiesManager.declineCookies()
    );
  }

  if (settingsBtn) {
    settingsBtn.addEventListener("click", () =>
      window.cookiesManager.showSettings()
    );
  }

  if (modalCloseBtn) {
    modalCloseBtn.addEventListener("click", () =>
      window.cookiesManager.hideSettings()
    );
  }

  if (saveSettingsBtn) {
    saveSettingsBtn.addEventListener("click", () =>
      window.cookiesManager.saveSettings()
    );
  }

  if (cancelSettingsBtn) {
    cancelSettingsBtn.addEventListener("click", () =>
      window.cookiesManager.hideSettings()
    );
  }

  // Close modal when clicking outside
  const modal = document.getElementById("cookiesModal");
  if (modal) {
    modal.addEventListener("click", function (e) {
      if (e.target === modal) {
        window.cookiesManager.hideSettings();
      }
    });
  }
});

// Expose reset function globally for testing/admin purposes
window.resetCookieConsent = function () {
  console.log("Resetting cookie consent");
  if (window.cookiesManager) {
    window.cookiesManager.resetConsent();
  } else {
    console.error("CookiesManager not found");
  }
};

// Add a test function to check if cookies are working
window.testCookies = function () {
  console.log("Testing cookies functionality");
  console.log("CookiesManager exists:", !!window.cookiesManager);
  console.log(
    "Banner element exists:",
    !!document.getElementById("cookiesBanner")
  );
  console.log(
    "Accept button exists:",
    !!document.getElementById("acceptCookies")
  );
  console.log(
    "Decline button exists:",
    !!document.getElementById("declineCookies")
  );
  console.log(
    "Settings button exists:",
    !!document.getElementById("cookieSettings")
  );

  if (window.cookiesManager) {
    console.log("Current settings:", window.cookiesManager.cookieSettings);
    console.log("Cookies accepted:", window.cookiesManager.cookiesAccepted);
    console.log("Cookies declined:", window.cookiesManager.cookiesDeclined);
  }
};
