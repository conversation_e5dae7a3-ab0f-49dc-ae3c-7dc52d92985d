# Generated by Django 5.2.1 on 2025-06-20 10:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0009_teammember'),
    ]

    operations = [
        migrations.CreateModel(
            name='TeamMemberPortfolioImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Image title/caption', max_length=200)),
                ('image', models.ImageField(help_text='Portfolio image', upload_to='team/portfolio/')),
                ('description', models.TextField(blank=True, help_text='Image description')),
                ('category', models.CharField(blank=True, help_text="Image category (e.g., 'Web Design', 'Branding')", max_length=100)),
                ('project_url', models.URLField(blank=True, help_text='Link to live project')),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('is_featured', models.<PERSON>oleanField(default=False, help_text='Featured in portfolio')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('team_member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='portfolio_images', to='netfyre_app.teammember')),
            ],
            options={
                'verbose_name': 'Portfolio Image',
                'verbose_name_plural': 'Portfolio Images',
                'ordering': ['order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TeamMemberPortfolioVideo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Video title', max_length=200)),
                ('video_file', models.FileField(blank=True, help_text='Upload video file', null=True, upload_to='team/videos/')),
                ('video_url', models.URLField(blank=True, help_text='Video URL (YouTube, Vimeo, etc.)')),
                ('thumbnail', models.ImageField(blank=True, help_text='Video thumbnail', null=True, upload_to='team/video_thumbnails/')),
                ('description', models.TextField(blank=True, help_text='Video description')),
                ('category', models.CharField(blank=True, help_text='Video category', max_length=100)),
                ('duration', models.CharField(blank=True, help_text="Video duration (e.g., '2:30')", max_length=20)),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('is_featured', models.BooleanField(default=False, help_text='Featured in portfolio')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('team_member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='portfolio_videos', to='netfyre_app.teammember')),
            ],
            options={
                'verbose_name': 'Portfolio Video',
                'verbose_name_plural': 'Portfolio Videos',
                'ordering': ['order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TeamMemberProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Project title', max_length=200)),
                ('description', models.TextField(help_text='Project description')),
                ('client', models.CharField(blank=True, help_text='Client name', max_length=100)),
                ('role', models.CharField(help_text='Your role in this project', max_length=100)),
                ('technologies', models.CharField(blank=True, help_text='Technologies used', max_length=300)),
                ('project_url', models.URLField(blank=True, help_text='Live project URL')),
                ('github_url', models.URLField(blank=True, help_text='GitHub repository URL')),
                ('featured_image', models.ImageField(help_text='Main project image', upload_to='team/projects/')),
                ('completion_date', models.DateField(help_text='Project completion date')),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('is_featured', models.BooleanField(default=False, help_text='Featured project')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('team_member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='projects', to='netfyre_app.teammember')),
            ],
            options={
                'verbose_name': 'Team Member Project',
                'verbose_name_plural': 'Team Member Projects',
                'ordering': ['order', '-completion_date'],
            },
        ),
    ]
