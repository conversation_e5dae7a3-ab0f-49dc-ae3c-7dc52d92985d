/* Case Study Special Navbar Styles */

/* Override base navbar for case study pages */
.case-study-page .modern-navbar {
  position: fixed !important;
  top: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 9999 !important;
  width: auto !important;
  max-width: 90vw !important;

  /* Special glass-morphism design */
  background: rgba(0, 0, 0, 0.85) !important;
  backdrop-filter: blur(25px) !important;
  -webkit-backdrop-filter: blur(25px) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  border-radius: 60px !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), 0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;

  /* Smooth transitions */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.case-study-page .modern-navbar:hover {
  background: rgba(0, 0, 0, 0.9) !important;
  border-color: rgba(255, 182, 0, 0.3) !important;
  box-shadow: 0 25px 70px rgba(0, 0, 0, 0.5), 0 10px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

.case-study-page .navbar-container {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 1rem 2rem !important;
  gap: 2.5rem !important;
}

/* Logo styling for case study navbar */
.case-study-page .navbar-logo .logo-img {
  height: 35px !important;
  width: auto !important;
  filter: brightness(1.2) !important;
  transition: all 0.3s ease !important;
}

.case-study-page .navbar-logo:hover .logo-img {
  filter: brightness(1.4) drop-shadow(0 0 10px rgba(255, 182, 0, 0.3)) !important;
  transform: scale(1.05) !important;
}

/* Navigation links styling */
.case-study-page .navbar-nav {
  flex: 1 !important;
  display: flex !important;
  justify-content: center !important;
}

.case-study-page .nav-links {
  display: flex !important;
  list-style: none !important;
  margin: 0 !important;
  padding: 0 !important;
  gap: 2rem !important;
}

.case-study-page .nav-link {
  color: #ffffff;
  text-decoration: none !important;
  font-weight: 500 !important;
  font-size: 0.9rem !important;
  padding: 0.5rem 1rem !important;
  border-radius: 25px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
}

.case-study-page .nav-link::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(
    90deg,
    transparent,
    rgb(255, 255, 255),
    transparent
  ) !important;
  transition: left 0.5s ease !important;
}

.case-study-page .nav-link:hover::before {
  left: 100% !important;
}

.case-study-page .nav-link:hover {
  color: #ffffff !important;
  background: rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1) !important;
}

.case-study-page .nav-link.active {
  color: #ffb600 !important;
  background: rgba(255, 182, 0, 0.15) !important;
  box-shadow: 0 4px 15px rgba(255, 182, 0, 0.2) !important;
}

/* CTA Button styling for case study navbar */
.case-study-page .navbar-cta .cta-button {
  background: linear-gradient(135deg, #ffb600 0%, #ff9500 100%) !important;
  color: #000 !important;
  text-decoration: none !important;
  padding: 0.7rem 1.5rem !important;
  border-radius: 30px !important;
  font-weight: 600 !important;
  font-size: 0.85rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 4px 15px rgba(255, 182, 0, 0.3) !important;
  border: none !important;
  position: relative !important;
  overflow: hidden !important;
}

.case-study-page .navbar-cta .cta-button::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  ) !important;
  transition: left 0.6s ease !important;
}

.case-study-page .navbar-cta .cta-button:hover::before {
  left: 100% !important;
}

.case-study-page .navbar-cta .cta-button:hover {
  transform: translateY(-2px) scale(1.02) !important;
  box-shadow: 0 8px 25px rgba(255, 182, 0, 0.4) !important;
  background: linear-gradient(135deg, #ffc700 0%, #ffa500 100%) !important;
}

/* Mobile menu toggle for case study navbar */
.case-study-page .mobile-menu-toggle {
  display: none !important;
  flex-direction: column !important;
  cursor: pointer !important;
  gap: 4px !important;
  padding: 8px !important;
  border-radius: 8px !important;
  transition: background-color 0.3s ease !important;
}

.case-study-page .mobile-menu-toggle span {
  width: 25px !important;
  height: 3px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 2px !important;
  transition: all 0.3s ease !important;
}

.case-study-page .mobile-menu-toggle:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.case-study-page .mobile-menu-toggle:hover span {
  background: #ffffff !important;
}

/* Mobile navigation menu for case study */
.case-study-page .mobile-nav-menu {
  position: fixed !important;
  top: 90px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 90vw !important;
  max-width: 400px !important;
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(25px) !important;
  -webkit-backdrop-filter: blur(25px) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  border-radius: 20px !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4) !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transform: translateX(-50%) translateY(-20px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  z-index: 9998 !important;
}

.case-study-page .mobile-nav-menu.active {
  opacity: 1 !important;
  visibility: visible !important;
  transform: translateX(-50%) translateY(0) !important;
}

/* Case study page mobile navigation - target by data attribute */
body[data-page="casestudy"] .mobile-nav-links {
  list-style: none !important;
  margin: 0 !important;
  padding: 1.5rem !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.5rem !important;
}

/* Make mobile nav links white on case study pages */
body[data-page="casestudy"] .mobile-nav-links .nav-link {
  color: #ffffff !important;
  padding: 1rem 1.5rem !important;
  border-radius: 15px !important;
  transition: all 0.3s ease !important;
  border: 1px solid transparent !important;
  text-decoration: none !important;
}

body[data-page="casestudy"] .mobile-nav-links .nav-link:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 182, 0, 0.3) !important;
  color: #ffffff !important;
}

/* Fallback for case study pages - more specific selector */
.mobile-nav-links .nav-link {
  color: #ffffff !important;
}

/* Override any other color rules - multiple selectors for maximum specificity */
body[data-page="casestudy"] .mobile-nav-menu .mobile-nav-links .nav-link,
body[data-page="casestudy"] .mobile-nav-menu .mobile-nav-links li .nav-link,
body[data-page="casestudy"] .mobile-nav-links .nav-link,
body[data-page="casestudy"] .mobile-nav-links li .nav-link {
  color: #200d0d !important;
  background-color: transparent !important;
}

/* Force white color on case study pages with highest specificity */
html body[data-page="casestudy"] .mobile-nav-menu .mobile-nav-links .nav-link {
  color: #ffffff !important;
}

/* Responsive design for case study navbar */
@media (max-width: 768px) {
  .case-study-page .modern-navbar {
    top: 15px !important;
    max-width: 95vw !important;
  }

  .case-study-page .navbar-container {
    padding: 0.8rem 1.5rem !important;
    gap: 1.5rem !important;
  }

  .case-study-page .navbar-nav {
    display: none !important;
  }

  .case-study-page .mobile-menu-toggle {
    display: flex !important;
  }

  .case-study-page .navbar-logo .logo-img {
    height: 30px !important;
  }

  .case-study-page .navbar-cta .cta-button {
    padding: 0.6rem 1.2rem !important;
    font-size: 0.8rem !important;
  }
}

@media (max-width: 480px) {
  .case-study-page .modern-navbar {
    top: 10px !important;
    max-width: 98vw !important;
  }

  .case-study-page .navbar-container {
    padding: 0.7rem 1.2rem !important;
    gap: 1rem !important;
  }

  .case-study-page .navbar-logo .logo-img {
    height: 28px !important;
  }

  .case-study-page .navbar-cta .cta-button {
    padding: 0.5rem 1rem !important;
    font-size: 0.75rem !important;
  }
}

/* Ensure case study content doesn't overlap with navbar */
.case-study-page {
  padding-top: 100px !important;
}

.case-study-page .casestudy-header {
  margin-top: 0 !important;
  padding-top: 40px !important;
}
