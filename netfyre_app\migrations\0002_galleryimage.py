# Generated by Django 5.2.1 on 2025-06-19 05:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='GalleryImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Image title/description', max_length=200)),
                ('image', models.ImageField(help_text='Upload gallery image', upload_to='gallery/')),
                ('column', models.PositiveIntegerField(choices=[(1, 'Column 1 (Scrolling Up)'), (2, 'Column 2 (Scrolling Down)'), (3, 'Column 3 (Scrolling Up)')], default=1, help_text='Which column to display in')),
                ('order', models.PositiveIntegerField(default=0, help_text='Order within the column')),
                ('alt_text', models.Char<PERSON><PERSON>(blank=True, help_text='Alt text for accessibility', max_length=200)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Gallery Image',
                'verbose_name_plural': 'Gallery Images',
                'ordering': ['column', 'order', 'title'],
            },
        ),
    ]
