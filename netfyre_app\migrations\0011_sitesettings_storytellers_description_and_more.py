# Generated by Django 5.2.1 on 2025-06-20 14:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0010_teammemberportfolioimage_teammemberportfoliovideo_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitesettings',
            name='storytellers_description',
            field=models.TextField(default="Behind every bold campaign and powerful strategy is a team of creative minds turning insights into impact. We're not just marketers—we're storytellers, strategists, designers, and data lovers dedicated to shaping your brand's narrative and driving real growth. Get to know the team that makes your success our mission.", help_text='Storytellers section description'),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='storytellers_title',
            field=models.CharField(default='Meet the Storytellers of Your Success', help_text='Storytellers section title', max_length=200),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='team_subtitle',
            field=models.TextField(default='Our tailored approach to brand success means better clicks, higher quality traffic, scroll-stopping ads, better ROAS and ultimately... more profit.', help_text='Team section description'),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='team_title',
            field=models.CharField(default='Increase Your Reach, Explode Your Sales.', help_text='Main team section title', max_length=200),
        ),
    ]
