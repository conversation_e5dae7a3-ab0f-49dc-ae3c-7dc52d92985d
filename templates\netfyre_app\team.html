{% extends 'base.html' %} {% load static %} {% block title %}Meet Our Team - {{
site_settings.site_title }}{% endblock %} {% block content %}
<!-- Team Hero Section -->
<section class="team-hero">
  <div class="team-hero-content">
    <h1>Meet the Storytellers of Your Success</h1>
    <p>
      Behind every bold campaign and powerful strategy is a team of creative
      minds turning insights into impact. We're not just marketers—we're
      storytellers, strategists, designers, and data lovers dedicated to shaping
      your brand's narrative and driving real growth.
    </p>
  </div>

  <!-- Featured Team Members Carousel -->
  <div class="featured-team-carousel">
    {% for member in featured_members %}
    <div class="featured-member-card">
      <div class="member-image-container">
        {% if member.profile_image %}
        <img
          src="{{ member.profile_image.url }}"
          alt="{{ member.name }}"
          class="member-image"
        />
        {% else %}
        <img
          src="{% static 'images/default-avatar.png' %}"
          alt="{{ member.name }}"
          class="member-image"
        />
        {% endif %}
        <div class="member-overlay">
          <h3>{{ member.name }}</h3>
          <p>{{ member.get_display_role }}</p>
        </div>
      </div>
    </div>
    {% empty %}
    <!-- Default featured members if none exist -->
    <div class="featured-member-card">
      <div class="member-image-container">
        <img
          src="https://images.pexels.com/photos/3777943/pexels-photo-3777943.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&dpr=1"
          alt="Team Member"
          class="member-image"
        />
        <div class="member-overlay">
          <h3>Sarah Johnson</h3>
          <p>Creative Director</p>
        </div>
      </div>
    </div>
    <div class="featured-member-card">
      <div class="member-image-container">
        <img
          src="https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&dpr=1"
          alt="Team Member"
          class="member-image"
        />
        <div class="member-overlay">
          <h3>Michael Chen</h3>
          <p>Lead Developer</p>
        </div>
      </div>
    </div>
    <div class="featured-member-card">
      <div class="member-image-container">
        <img
          src="https://images.pexels.com/photos/3225528/pexels-photo-3225528.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&dpr=1"
          alt="Team Member"
          class="member-image"
        />
        <div class="member-overlay">
          <h3>Emily Rodriguez</h3>
          <p>Marketing Strategist</p>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>
</section>

<!-- Team Stats Section -->
<section class="team-stats">
  <div class="stats-container">
    <div class="stat-item">
      <div class="stat-number">{{ team_members.count }}+</div>
      <div class="stat-label">Team Members</div>
    </div>
    <div class="stat-item">
      <div class="stat-number">{{ team_members|length|add:"5" }}+</div>
      <div class="stat-label">Years Combined Experience</div>
    </div>
    <div class="stat-item">
      <div class="stat-number">100+</div>
      <div class="stat-label">Projects Delivered</div>
    </div>
    <div class="stat-item">
      <div class="stat-number">24/7</div>
      <div class="stat-label">Support Available</div>
    </div>
  </div>
</section>

<!-- Leadership Team -->
{% if founders %}
<section class="team-section leadership-section">
  <div class="section-header">
    <h2>Leadership Team</h2>
    <p>The visionaries driving our mission forward</p>
  </div>

  <div class="team-grid leadership-grid">
    {% for member in founders %}
    <div
      class="team-member-card leadership-card"
      onclick="window.location.href='{% url 'netfyre_app:team_member_detail' member.pk %}'"
    >
      <div class="member-card-inner">
        <div class="member-front">
          {% if member.profile_image %}
          <img
            src="{{ member.profile_image.url }}"
            alt="{{ member.name }}"
            class="member-photo"
          />
          {% else %}
          <img
            src="{% static 'images/default-avatar.png' %}"
            alt="{{ member.name }}"
            class="member-photo"
          />
          {% endif %}
          <div class="member-info">
            <h3>{{ member.name }}</h3>
            <p class="member-role">{{ member.get_display_role }}</p>
            <p class="member-experience">
              {{ member.years_experience }} years experience
            </p>
            <div class="view-portfolio-hint">Click to view portfolio</div>
          </div>
        </div>
        <div class="member-back">
          <div class="member-details">
            <h3>{{ member.name }}</h3>
            <p class="member-bio">{{ member.short_bio }}</p>
            <div class="member-specialties">
              {% for specialty in member.get_specialties_list %}
              <span class="specialty-tag">{{ specialty }}</span>
              {% endfor %}
            </div>
            <div class="portfolio-stats">
              <div class="stat">
                <span class="stat-number"
                  >{{ member.portfolio_images.count }}</span
                >
                <span class="stat-label">Images</span>
              </div>
              <div class="stat">
                <span class="stat-number"
                  >{{ member.portfolio_videos.count }}</span
                >
                <span class="stat-label">Videos</span>
              </div>
              <div class="stat">
                <span class="stat-number">{{ member.projects.count }}</span>
                <span class="stat-label">Projects</span>
              </div>
            </div>
            {% if member.show_contact %}
            <div class="member-social">
              {% if member.linkedin_url %}<a
                href="{{ member.linkedin_url }}"
                target="_blank"
                class="social-link linkedin"
                onclick="event.stopPropagation();"
                >LinkedIn</a
              >{% endif %} {% if member.twitter_url %}<a
                href="{{ member.twitter_url }}"
                target="_blank"
                class="social-link twitter"
                onclick="event.stopPropagation();"
                >Twitter</a
              >{% endif %} {% if member.email %}<a
                href="mailto:{{ member.email }}"
                class="social-link email"
                onclick="event.stopPropagation();"
                >Email</a
              >{% endif %}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>
</section>
{% endif %}

<!-- Design Team -->
{% if designers %}
<section class="team-section design-section">
  <div class="section-header">
    <h2>Creative Design Team</h2>
    <p>Bringing your vision to life with stunning visuals</p>
  </div>

  <div class="team-grid">
    {% for member in designers %}
    <div
      class="team-member-card"
      onclick="window.location.href='{% url 'netfyre_app:team_member_detail' member.pk %}'"
    >
      <div class="member-card-inner">
        <div class="member-front">
          {% if member.profile_image %}
          <img
            src="{{ member.profile_image.url }}"
            alt="{{ member.name }}"
            class="member-photo"
          />
          {% else %}
          <img
            src="{% static 'images/default-avatar.png' %}"
            alt="{{ member.name }}"
            class="member-photo"
          />
          {% endif %}
          <div class="member-info">
            <h3>{{ member.name }}</h3>
            <p class="member-role">{{ member.get_display_role }}</p>
            <p class="member-experience">
              {{ member.years_experience }} years experience
            </p>
          </div>
        </div>
        <div class="member-back">
          <div class="member-details">
            <h3>{{ member.name }}</h3>
            <p class="member-bio">{{ member.short_bio }}</p>
            <div class="member-specialties">
              {% for specialty in member.get_specialties_list %}
              <span class="specialty-tag">{{ specialty }}</span>
              {% endfor %}
            </div>
            {% if member.show_contact %}
            <div class="member-social">
              {% if member.behance_url %}<a
                href="{{ member.behance_url }}"
                target="_blank"
                class="social-link behance"
                >Behance</a
              >{% endif %} {% if member.dribbble_url %}<a
                href="{{ member.dribbble_url }}"
                target="_blank"
                class="social-link dribbble"
                >Dribbble</a
              >{% endif %} {% if member.linkedin_url %}<a
                href="{{ member.linkedin_url }}"
                target="_blank"
                class="social-link linkedin"
                >LinkedIn</a
              >{% endif %}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>
</section>
{% endif %}

<!-- Development Team -->
{% if developers %}
<section class="team-section development-section">
  <div class="section-header">
    <h2>Development Team</h2>
    <p>Building robust, scalable solutions that perform</p>
  </div>

  <div class="team-grid">
    {% for member in developers %}
    <div
      class="team-member-card"
      onclick="window.location.href='{% url 'netfyre_app:team_member_detail' member.pk %}'"
    >
      <div class="member-card-inner">
        <div class="member-front">
          {% if member.profile_image %}
          <img
            src="{{ member.profile_image.url }}"
            alt="{{ member.name }}"
            class="member-photo"
          />
          {% else %}
          <img
            src="{% static 'images/default-avatar.png' %}"
            alt="{{ member.name }}"
            class="member-photo"
          />
          {% endif %}
          <div class="member-info">
            <h3>{{ member.name }}</h3>
            <p class="member-role">{{ member.get_display_role }}</p>
            <p class="member-experience">
              {{ member.years_experience }} years experience
            </p>
          </div>
        </div>
        <div class="member-back">
          <div class="member-details">
            <h3>{{ member.name }}</h3>
            <p class="member-bio">{{ member.short_bio }}</p>
            <div class="member-specialties">
              {% for specialty in member.get_specialties_list %}
              <span class="specialty-tag">{{ specialty }}</span>
              {% endfor %}
            </div>
            {% if member.show_contact %}
            <div class="member-social">
              {% if member.github_url %}<a
                href="{{ member.github_url }}"
                target="_blank"
                class="social-link github"
                >GitHub</a
              >{% endif %} {% if member.linkedin_url %}<a
                href="{{ member.linkedin_url }}"
                target="_blank"
                class="social-link linkedin"
                >LinkedIn</a
              >{% endif %} {% if member.personal_website %}<a
                href="{{ member.personal_website }}"
                target="_blank"
                class="social-link website"
                >Website</a
              >{% endif %}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>
</section>
{% endif %}

<!-- Marketing Team -->
{% if marketers %}
<section class="team-section marketing-section">
  <div class="section-header">
    <h2>Marketing Team</h2>
    <p>Driving growth through data-driven strategies</p>
  </div>

  <div class="team-grid">
    {% for member in marketers %}
    <div
      class="team-member-card"
      onclick="window.location.href='{% url 'netfyre_app:team_member_detail' member.pk %}'"
    >
      <div class="member-card-inner">
        <div class="member-front">
          {% if member.profile_image %}
          <img
            src="{{ member.profile_image.url }}"
            alt="{{ member.name }}"
            class="member-photo"
          />
          {% else %}
          <img
            src="{% static 'images/default-avatar.png' %}"
            alt="{{ member.name }}"
            class="member-photo"
          />
          {% endif %}
          <div class="member-info">
            <h3>{{ member.name }}</h3>
            <p class="member-role">{{ member.get_display_role }}</p>
            <p class="member-experience">
              {{ member.years_experience }} years experience
            </p>
          </div>
        </div>
        <div class="member-back">
          <div class="member-details">
            <h3>{{ member.name }}</h3>
            <p class="member-bio">{{ member.short_bio }}</p>
            <div class="member-specialties">
              {% for specialty in member.get_specialties_list %}
              <span class="specialty-tag">{{ specialty }}</span>
              {% endfor %}
            </div>
            {% if member.show_contact %}
            <div class="member-social">
              {% if member.linkedin_url %}<a
                href="{{ member.linkedin_url }}"
                target="_blank"
                class="social-link linkedin"
                >LinkedIn</a
              >{% endif %} {% if member.twitter_url %}<a
                href="{{ member.twitter_url }}"
                target="_blank"
                class="social-link twitter"
                >Twitter</a
              >{% endif %} {% if member.instagram_url %}<a
                href="{{ member.instagram_url }}"
                target="_blank"
                class="social-link instagram"
                >Instagram</a
              >{% endif %}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>
</section>
{% endif %}

<!-- All Team Members Grid (if no specific roles) -->
{% if not founders and not designers and not developers and not marketers %}
<section class="team-section all-team-section">
  <div class="section-header">
    <h2>Our Amazing Team</h2>
    <p>Meet the talented individuals who make the magic happen</p>
  </div>

  <div class="team-grid">
    {% for member in team_members %}
    <div
      class="team-member-card"
      onclick="window.location.href='{% url 'netfyre_app:team_member_detail' member.pk %}'"
    >
      <div class="member-card-inner">
        <div class="member-front">
          {% if member.profile_image %}
          <img
            src="{{ member.profile_image.url }}"
            alt="{{ member.name }}"
            class="member-photo"
          />
          {% else %}
          <img
            src="{% static 'images/default-avatar.png' %}"
            alt="{{ member.name }}"
            class="member-photo"
          />
          {% endif %}
          <div class="member-info">
            <h3>{{ member.name }}</h3>
            <p class="member-role">{{ member.get_display_role }}</p>
            <p class="member-experience">
              {{ member.years_experience }} years experience
            </p>
          </div>
        </div>
        <div class="member-back">
          <div class="member-details">
            <h3>{{ member.name }}</h3>
            <p class="member-bio">{{ member.short_bio }}</p>
            <div class="member-specialties">
              {% for specialty in member.get_specialties_list %}
              <span class="specialty-tag">{{ specialty }}</span>
              {% endfor %}
            </div>
            {% if member.show_contact %}
            <div class="member-social">
              {% if member.linkedin_url %}<a
                href="{{ member.linkedin_url }}"
                target="_blank"
                class="social-link linkedin"
                >LinkedIn</a
              >{% endif %} {% if member.email %}<a
                href="mailto:{{ member.email }}"
                class="social-link email"
                >Email</a
              >{% endif %}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    {% empty %}
    <!-- Default team members if none exist -->
    <div class="team-member-card">
      <div class="member-card-inner">
        <div class="member-front">
          <img
            src="https://images.pexels.com/photos/3777943/pexels-photo-3777943.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&dpr=1"
            alt="Sarah Johnson"
            class="member-photo"
          />
          <div class="member-info">
            <h3>Sarah Johnson</h3>
            <p class="member-role">Creative Director</p>
            <p class="member-experience">8 years experience</p>
          </div>
        </div>
        <div class="member-back">
          <div class="member-details">
            <h3>Sarah Johnson</h3>
            <p class="member-bio">
              Passionate about creating stunning visual experiences that drive
              results.
            </p>
            <div class="member-specialties">
              <span class="specialty-tag">UI/UX Design</span>
              <span class="specialty-tag">Brand Identity</span>
              <span class="specialty-tag">Creative Strategy</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>
</section>
{% endif %} {% endblock %}
