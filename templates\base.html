{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}{{ site_settings.site_title }}{% endblock %}</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@600&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="{% static 'css/style.css' %}" />
    {% block extra_css %}{% endblock %} {% if site_settings.favicon %}
    <link
      rel="icon"
      type="image/x-icon"
      href="{{ site_settings.favicon.url }}"
    />
    {% endif %}
  </head>
  <body{% block body_attributes %}{% endblock %}>
    <!-- Modern Navigation Bar -->
    <header class="modern-navbar">
      <div class="navbar-container">
        <!-- Logo Section -->
        <div class="navbar-logo">
          <a
            href="{{ navbar_settings.brand_link|default:'/' }}"
            class="logo-link"
          >
            {% if navbar_settings.logo %}
            <img
              src="{{ navbar_settings.logo.url }}"
              alt="{{ navbar_settings.logo_alt_text }}"
              class="logo-img"
              style="width: {{ navbar_settings.logo_width|default:120 }}px; height: {{ navbar_settings.logo_height|default:40 }}px;"
            />
            {% elif site_settings.favicon %}
            <img
              src="{{ site_settings.favicon.url }}"
              alt="{{ navbar_settings.logo_alt_text|default:'NetFyre' }}"
              class="logo-img"
            />
            {% else %}
            <img
              src="{% static 'images/buttonlogo.png' %}"
              alt="{{ navbar_settings.logo_alt_text|default:'NetFyre' }}"
              class="logo-img"
            />
            {% endif %}
          </a>
        </div>

        <!-- Navigation Links -->
        <nav class="navbar-nav">
          <ul class="nav-links">
            <li>
              <a href="{% url 'netfyre_app:home' %}" class="nav-link">Home</a>
            </li>
            <li>
              <a
                href="{% url 'netfyre_app:casestudies_list' %}"
                class="nav-link"
                >Case Studies</a
              >
            </li>
            <li><a href="#about" class="nav-link">About</a></li>
            <li>
              <a href="{% url 'netfyre_app:team' %}" class="nav-link">Team</a>
            </li>
            <li><a href="#services-ticker" class="nav-link">Services</a></li>
            <li><a href="#testimonial" class="nav-link">Reviews</a></li>
          </ul>
        </nav>

        <!-- CTA Button -->
        <div class="navbar-cta">
          <a href="{% url 'netfyre_app:contact' %}" class="cta-button"
            >Get Started</a
          >
        </div>

        <!-- Mobile Menu Toggle -->
        <div class="mobile-menu-toggle" id="mobileMenuToggle">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>

      <!-- Mobile Navigation Menu -->
      <div class="mobile-nav-menu" id="mobileNavMenu">
        <ul class="mobile-nav-links">
          <li>
            <a href="{% url 'netfyre_app:home' %}" class="nav-link">Home</a>
          </li>
          <li>
            <a href="{% url 'netfyre_app:casestudies_list' %}" class="nav-link"
              >Case Studies</a
            >
          </li>
          <li>
            <a href="#about" class="nav-link">About</a>
          </li>
          <li>
            <a href="{% url 'netfyre_app:team' %}" class="nav-link">Team</a>
          </li>
          <li>
            <a href="#services-ticker" class="nav-link">Services</a>
          </li>
          <li>
            <a href="#testimonial" class="nav-link">Reviews</a>
          </li>
          <li>
            <a href="{% url 'netfyre_app:contact' %}" class="nav-link"
              >Contact</a
            >
          </li>
        </ul>
      </div>
    </header>

    {% block content %} {% endblock %}

    <!-- Footer Section -->
    <footer class="footer">
      <div class="footer-container">
        <div class="footer-brand">
          <h1 class="brand-text">{{ site_settings.site_title }}</h1>
        </div>
        <div class="footer-left">
          <div class="footer-inquiries">
            <a
              href="mailto:{{ site_settings.contact_email }}"
              class="footer-email"
              >{{ site_settings.contact_email }}</a
            >
          </div>
        </div>

        <div class="footer-right">
          <div class="footer-navigation">
            <div class="footer-nav-links">
              <a
                href="{% url 'netfyre_app:casestudies_list' %}"
                class="footer-link"
                >Projects</a
              >

                <a href="{% url 'netfyre_app:team' %}" class="footer-link"
                >Team</a
                >
              <a href="{% url 'netfyre_app:contact' %}" class="footer-link"
                >Contact</a
              >
              <a href="{% url 'netfyre_app:home' %}" class="footer-link"
                >Home</a
              >
            </div>
          </div>

          <div class="footer-socials">
            <span class="footer-label">Follow Us</span>
            <div class="footer-social-links">
              <a
                href="https://www.linkedin.com/company/netfyre/"
                target="_blank"
                rel="noopener noreferrer"
                class="footer-link"
                >LinkedIn</a
              >
              <a
                href="{% url 'netfyre_app:terms_conditions' %}"
                class="footer-link"
                >Terms and conditions</a
              >
              <a
                href="{% url 'netfyre_app:privacy_policy' %}"
                class="footer-link"
                >Privacy Policy</a
              >
            </div>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <div class="footer-copyright">
          <span>© {% now "Y" %} {{ site_settings.site_title|upper }}</span>
        </div>
      </div>
    </footer>

    <!-- Cookies Banner -->
    <div id="cookiesBanner" class="cookies-banner">
      <div class="cookies-container">
        <div class="cookies-content">
          <h3 class="cookies-title">🍪 We use cookies</h3>
          <p class="cookies-text">
            We use cookies to enhance your browsing experience, serve
            personalized content, and analyze our traffic. By clicking "Accept
            All", you consent to our use of cookies.
            <a href="{% url 'netfyre_app:privacy_policy' %}"
              >Read our Privacy Policy</a
            >
          </p>
        </div>
        <div class="cookies-actions">
          <button id="acceptCookies" class="cookies-btn cookies-btn-accept">
            Accept All
          </button>
          <button id="declineCookies" class="cookies-btn cookies-btn-decline">
            Decline
          </button>
          <button id="cookieSettings" class="cookies-btn cookies-btn-settings">
            Settings
          </button>
        </div>
      </div>
    </div>

    <!-- Cookies Settings Modal -->
    <div id="cookiesModal" class="cookies-modal">
      <div class="cookies-modal-content">
        <div class="cookies-modal-header">
          <h2 class="cookies-modal-title">Cookie Settings</h2>
          <button id="closeModal" class="cookies-modal-close">&times;</button>
        </div>

        <div class="cookies-category">
          <div class="cookies-category-header">
            <h3 class="cookies-category-title">Necessary Cookies</h3>
            <label class="cookies-toggle">
              <input type="checkbox" id="toggle-necessary" checked disabled />
              <span class="cookies-toggle-slider"></span>
            </label>
          </div>
          <p class="cookies-category-description">
            These cookies are essential for the website to function properly.
            They enable basic features like page navigation and access to secure
            areas.
          </p>
        </div>

        <div class="cookies-category">
          <div class="cookies-category-header">
            <h3 class="cookies-category-title">Analytics Cookies</h3>
            <label class="cookies-toggle">
              <input type="checkbox" id="toggle-analytics" />
              <span class="cookies-toggle-slider"></span>
            </label>
          </div>
          <p class="cookies-category-description">
            These cookies help us understand how visitors interact with our
            website by collecting and reporting information anonymously.
          </p>
        </div>

        <div class="cookies-category">
          <div class="cookies-category-header">
            <h3 class="cookies-category-title">Marketing Cookies</h3>
            <label class="cookies-toggle">
              <input type="checkbox" id="toggle-marketing" />
              <span class="cookies-toggle-slider"></span>
            </label>
          </div>
          <p class="cookies-category-description">
            These cookies are used to deliver advertisements more relevant to
            you and your interests. They may also be used to limit the number of
            times you see an advertisement.
          </p>
        </div>

        <div class="cookies-category">
          <div class="cookies-category-header">
            <h3 class="cookies-category-title">Preference Cookies</h3>
            <label class="cookies-toggle">
              <input type="checkbox" id="toggle-preferences" />
              <span class="cookies-toggle-slider"></span>
            </label>
          </div>
          <p class="cookies-category-description">
            These cookies allow the website to remember choices you make (such
            as your user name, language, or the region you are in) and provide
            enhanced features.
          </p>
        </div>

        <div class="cookies-modal-actions">
          <button
            id="saveSettings"
            class="cookies-modal-btn cookies-modal-btn-save"
          >
            Save Settings
          </button>
          <button
            id="cancelSettings"
            class="cookies-modal-btn cookies-modal-btn-cancel"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>

    <script src="{% static 'js/script.js' %}"></script>
    <script src="{% static 'js/cookies.js' %}"></script>
    {% block extra_js %}{% endblock %}
  </body>
</html>
