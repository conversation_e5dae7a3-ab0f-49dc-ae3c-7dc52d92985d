{% extends 'base.html' %} {% load static %} {% block content %}
<!-- Hero Section -->
<section
  class="hero-section"
  data-bg-color="#000"
  data-text-color="#fff"
  data-accent-color="#ffd700"
>
  <!-- Background animated text -->
  <div class="hero-bg-text" data-animation-speed="15s">
    <!-- Repeat text multiple times for seamless loop -->
     <span class="bg-text-row" data-direction="right"
      >YOUR YOUR YOUR YOUR YOUR YOUR</span
    >
    <span class="bg-text-row" data-direction="left"
      >COMPLETE COMPLETE COMPLETE COMPLETE COMPLETE COMPLETE</span
    >
    <span class="bg-text-row" data-direction="right"
      >E COMMERCE E COMMERCE E COMMERCE E COMMERCE E COMMERCE E COMMERCE</span
    >
    <span class="bg-text-row" data-direction="left"
      >DIGITAL DIGITAL DIGITAL DIGITAL DIGITAL DIGITAL</span
    >
    <span class="bg-text-row" data-direction="right"
      >GROWTH GROWTH GROWTH GROWTH GROWTH GROWTH</span
    >
    <span class="bg-text-row" data-direction="left"
      >PARTNER PARTNER PARTNER PARTNER PARTEER PARTNER</span
    >
    
  </div>

  <div class="hero-container">
    <!-- Left content area -->
    <div class="hero-content">
      <!-- Main logo/title -->
      <div class="hero-logo-container">
        {% if site_settings.logo %}
        <img
          src="{{ site_settings.logo.url }}"
          alt="{{ site_settings.site_title }} Logo"
          class="hero-logo"
          data-animate="true"
        />
        {% else %}
        <img
          src="{% static 'images/logo.png' %}"
          alt="{{ site_settings.site_title }} Logo"
          class="hero-logo"
          data-animate="true"
        />
        {% endif %}
      </div>

      <!-- Call-to-action button -->
      <button
        class="hero-cta-btn"
        data-text="BOOK A CALL"
        data-hover-text="LET'S TALK!"
        onclick="handleCTAClick()"
      >
        <img
          src="{% static 'images/buttonlogo.png' %}"
          alt="CTA Icon"
          class="cta-icon"
          width="30"
          height="30"
        />
        <div class="cta-badge">YOU</div>
        <span class="cta-text default-text">BOOK A CALL</span>
        <span class="cta-text hover-text">LET'S TALK!</span>
      </button>
    </div>

    <!-- Right image gallery -->
    <div class="hero-gallery" data-columns="3" data-animation-speed="15s">
      <!-- Column 1 - Scrolling up -->
      <div class="gallery-column" data-direction="up">
        <div class="gallery-track">
          {% for i in "12" %}
            {% if hero_gallery_column_1 %}
              {% for project in hero_gallery_column_1 %}
                {% if project.image %}
                  <img src="{{ project.image.url }}" alt="{{ project.title }}" class="gallery-img" />
                {% endif %}
              {% endfor %}
            {% else %}
              <!-- fallback images duplicated -->
              <img src="{% static 'images/img1.jpeg' %}" class="gallery-img" />
              <img src="{% static 'images/img2.jpeg' %}" class="gallery-img" />
              <img src="{% static 'images/img3.jpeg' %}" class="gallery-img" />
              <img src="{% static 'images/img4.jpeg' %}" class="gallery-img" />
              <img src="{% static 'images/img5.jpeg' %}" class="gallery-img" />
              <img src="{% static 'images/img6.jpeg' %}" class="gallery-img" />
            {% endif %}
          {% endfor %}
        </div>
      </div>


      <!-- Column 2 - Scrolling down -->
  <div class="gallery-column" data-direction="down">
  <div class="gallery-track">
    {% for i in "12" %}
      {% if hero_gallery_column_1 %}
        {% for project in hero_gallery_column_1 %}
          {% if project.image %}
            <img src="{{ project.image.url }}" alt="{{ project.title }}" class="gallery-img" />
          {% endif %}
        {% endfor %}
      {% else %}
        <!-- fallback images duplicated -->
        <img src="{% static 'images/img1.jpeg' %}" class="gallery-img" />
        <img src="{% static 'images/img2.jpeg' %}" class="gallery-img" />
        <img src="{% static 'images/img3.jpeg' %}" class="gallery-img" />
        <img src="{% static 'images/img4.jpeg' %}" class="gallery-img" />
        <img src="{% static 'images/img5.jpeg' %}" class="gallery-img" />
        <img src="{% static 'images/img6.jpeg' %}" class="gallery-img" />
      {% endif %}
    {% endfor %}
  </div>
</div>



      <!-- Column 3 - Scrolling up -->
      <div class="gallery-column" data-direction="up">
  <div class="gallery-track">
    {% for i in "12" %}
      {% if hero_gallery_column_1 %}
        {% for project in hero_gallery_column_1 %}
          {% if project.image %}
            <img src="{{ project.image.url }}" alt="{{ project.title }}" class="gallery-img" />
          {% endif %}
        {% endfor %}
      {% else %}
        <!-- fallback images duplicated -->
        <img src="{% static 'images/img1.jpeg' %}" class="gallery-img" />
        <img src="{% static 'images/img2.jpeg' %}" class="gallery-img" />
        <img src="{% static 'images/img3.jpeg' %}" class="gallery-img" />
        <img src="{% static 'images/img4.jpeg' %}" class="gallery-img" />
        <img src="{% static 'images/img5.jpeg' %}" class="gallery-img" />
        <img src="{% static 'images/img6.jpeg' %}" class="gallery-img" />
      {% endif %}
    {% endfor %}
  </div>
</div>


  </div>
</section>

<!-- Team Section -->
<section id="team" class="team">
  <h1>
    {% if site_settings.team_title %}
      {{ site_settings.team_title|safe }}
    {% else %}
      Increase Your Reach,<br />
      Explode Your <span class="center-line">Sales.</span>
    {% endif %}
  </h1>
  <p>
    {% if site_settings.team_subtitle %}
      {{ site_settings.team_subtitle }}
    {% else %}
      Our tailored approach to brand success means better clicks, higher quality
      traffic, scroll-stopping ads, better ROAS and ultimately... more profit.
    {% endif %}
  </p>
  <div class="storytellers-section">
    <div class="storytellers-content">
      <h2>
        {% if site_settings.storytellers_title %}
          {{ site_settings.storytellers_title }}
        {% else %}
          Meet the Storytellers of Your Success
        {% endif %}
      </h2>
      <p>
        {% if site_settings.storytellers_description %}
          {{ site_settings.storytellers_description }}
        {% else %}
          Behind every bold campaign and powerful strategy is a team of creative
          minds turning insights into impact. We're not just marketers—we're
          storytellers, strategists, designers, and data lovers dedicated to
          shaping your brand's narrative and driving real growth. Get to know the
          team that makes your success our mission.
        {% endif %}
      </p>
      <div class="action-buttons">
        <a href="{% url 'netfyre_app:team' %}" class="meet-btn">Meet →</a>
      </div>
    </div>
    <div class="image-gallery">
      <div class="image-column column-1">
        {% for image in gallery_column_1 %} {% if image.image %}
        <img src="{{ image.image.url }}" alt="{{ image.alt_text }}" />
        {% endif %} {% empty %}
        <!-- First set of images -->
        <img
          src="https://images.pexels.com/photos/3777943/pexels-photo-3777943.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Abstract art piece"
        />
        <img
          src="https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Portrait of a person"
        />
        <img
          src="https://images.pexels.com/photos/3225528/pexels-photo-3225528.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Pink architectural structure"
        />
        <img
          src="https://images.pexels.com/photos/2887718/pexels-photo-2887718.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Beige architectural detail"
        />
        <img
          src="https://images.pexels.com/photos/3225517/pexels-photo-3225517.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Lush landscape"
        />
        <img
          src="https://images.pexels.com/photos/1576937/pexels-photo-1576937.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Woman with flowers"
        />
        <!-- Duplicate set for seamless loop -->
        <img
          src="https://images.pexels.com/photos/3777943/pexels-photo-3777943.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Abstract art piece"
        />
        <img
          src="https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Portrait of a person"
        />
        <img
          src="https://images.pexels.com/photos/3225528/pexels-photo-3225528.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Pink architectural structure"
        />
        <img
          src="https://images.pexels.com/photos/2887718/pexels-photo-2887718.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Beige architectural detail"
        />
        <img
          src="https://images.pexels.com/photos/3225517/pexels-photo-3225517.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Lush landscape"
        />
        <img
          src="https://images.pexels.com/photos/1576937/pexels-photo-1576937.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Woman with flowers"
        />
        <!-- Additional images for better horizontal scrolling -->
        <img
          src="https://images.pexels.com/photos/3777943/pexels-photo-3777943.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Abstract art piece"
        />
        <img
          src="https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Portrait of a person"
        />
        {% endfor %}
      </div>
      <div class="image-column column-2">
        {% for image in gallery_column_2 %} {% if image.image %}
        <img src="{{ image.image.url }}" alt="{{ image.alt_text }}" />
        {% endif %} {% empty %}
        <!-- First set of images -->
        <img
          src="https://images.pexels.com/photos/3225517/pexels-photo-3225517.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Lush landscape with a river"
        />
        <img
          src="https://images.pexels.com/photos/1576937/pexels-photo-1576937.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Woman with a crown of yellow flowers"
        />
        <img
          src="https://images.pexels.com/photos/3777943/pexels-photo-3777943.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Abstract art piece"
        />
        <img
          src="https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Portrait of a person"
        />
        <img
          src="https://images.pexels.com/photos/2887718/pexels-photo-2887718.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Beige architectural detail"
        />
        <img
          src="https://images.pexels.com/photos/3225528/pexels-photo-3225528.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Pink architectural structure"
        />
        <!-- Duplicate set for seamless loop -->
        <img
          src="https://images.pexels.com/photos/3225517/pexels-photo-3225517.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Lush landscape with a river"
        />
        <img
          src="https://images.pexels.com/photos/1576937/pexels-photo-1576937.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Woman with a crown of yellow flowers"
        />
        <img
          src="https://images.pexels.com/photos/3777943/pexels-photo-3777943.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Abstract art piece"
        />
        <img
          src="https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Portrait of a person"
        />
        <img
          src="https://images.pexels.com/photos/2887718/pexels-photo-2887718.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Beige architectural detail"
        />
        <img
          src="https://images.pexels.com/photos/3225528/pexels-photo-3225528.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Pink architectural structure"
        />
        <!-- Additional images for better horizontal scrolling -->
        <img
          src="https://images.pexels.com/photos/3225517/pexels-photo-3225517.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Lush landscape with a river"
        />
        <img
          src="https://images.pexels.com/photos/1576937/pexels-photo-1576937.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt="Woman with a crown of yellow flowers"
        />
        {% endfor %}
      </div>
    </div>
  </div>
  <div class="trusted-by">
    <p>Trusted by:</p>
    <div class="logos">
      <div class="logos-track">
        {% for logo in company_logos %} {% if logo.logo_image %}
        <img
          src="{{ logo.logo_image.url }}"
          alt="{{ logo.company_name }}"
          class="logo-img"
        />
        {% endif %} {% empty %}
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Nike-Logo.png"
          alt="Nike"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Netflix-Logo.png"
          alt="Netflix"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Google-Logo.png"
          alt="Google"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Apple-Logo.png"
          alt="Apple"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Microsoft-Logo.png"
          alt="Microsoft"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Amazon-Logo.png"
          alt="Amazon"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Facebook-Logo.png"
          alt="Meta"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Tesla-Logo.png"
          alt="Tesla"
          class="logo-img"
        />
        <!-- Duplicate set for seamless loop -->
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Nike-Logo.png"
          alt="Nike"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Netflix-Logo.png"
          alt="Netflix"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Google-Logo.png"
          alt="Google"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Apple-Logo.png"
          alt="Apple"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Microsoft-Logo.png"
          alt="Microsoft"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Amazon-Logo.png"
          alt="Amazon"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Facebook-Logo.png"
          alt="Meta"
          class="logo-img"
        />
        <img
          src="https://logos-world.net/wp-content/uploads/2020/04/Tesla-Logo.png"
          alt="Tesla"
          class="logo-img"
        />
        {% endfor %}
      </div>
    </div>
  </div>
</section>

<!-- About Section -->
<section id="about">
  <h1>{{ about_section.title }}</h1>
  <h6 class="subheading">{{ about_section.subtitle }}</h6>

  <div class="video-container">
    {% if about_section.video_file %}
      <!-- Use uploaded video file -->
      <video
        {% if about_section.controls %}controls{% endif %}
        {% if about_section.autoplay %}autoplay{% endif %}
        {% if about_section.muted %}muted{% endif %}
        {% if about_section.loop %}loop{% endif %}
        playsinline
        {% if about_section.video_poster %}poster="{{ about_section.video_poster.url }}"{% endif %}
      >
        <source src="{{ about_section.video_file.url }}" type="video/mp4" />
        Your browser does not support the video tag.
      </video>
    {% elif about_section.video_url %}
      <!-- Handle YouTube and other video URLs -->
      {% if about_section.is_youtube_url %}
        <!-- YouTube embed -->
        <iframe
          width="100%"
          height="100%"
          src="{{ about_section.get_youtube_embed_url }}"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen
        ></iframe>
      {% else %}
        <!-- Direct video URL -->
        <video
          {% if about_section.controls %}controls{% endif %}
          {% if about_section.autoplay %}autoplay{% endif %}
          {% if about_section.muted %}muted{% endif %}
          {% if about_section.loop %}loop{% endif %}
          playsinline
          {% if about_section.video_poster %}poster="{{ about_section.video_poster.url }}"{% endif %}
        >
          <source src="{{ about_section.video_url }}" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      {% endif %}
    {% else %}
      <!-- Fallback video -->
      <video
        {% if about_section.controls %}controls{% endif %}
        {% if about_section.autoplay %}autoplay{% endif %}
        {% if about_section.muted %}muted{% endif %}
        {% if about_section.loop %}loop{% endif %}
        playsinline
      >
        <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>
    {% endif %}
  </div>

  {% if about_section.description %}
  <div class="about-description">
    <p>{{ about_section.description|linebreaks }}</p>
  </div>
  {% endif %}

  <div class="counter-section">
    {% for counter in counters %}
    <div class="counter-box">
      <div
        class="counter"
        data-target="{{ counter.value }}"
        data-prefix="{{ counter.prefix }}"
        data-plus="{% if counter.suffix == '+' %}true{% else %}false{% endif %}"
      >
        0
      </div>
      <div class="label">{{ counter.label }}</div>
    </div>
    {% empty %}
    <div class="counter-box">
      <div class="counter" data-target="39987" data-prefix="$" data-plus="true">
        0
      </div>
      <div class="label">TOTAL ROI</div>
    </div>
    <div class="counter-box">
      <div class="counter" data-target="500" data-prefix="$" data-plus="true">
        0
      </div>
      <div class="label">Daily Spend</div>
    </div>
    <div class="counter-box">
      <div class="counter" data-target="21" data-prefix="" data-plus="false">
        0
      </div>
      <div class="label">Clients Served</div>
    </div>
    {% endfor %}
  </div>
</section>

<!-- Animated Services Ticker Section -->
<section class="services-ticker-section" id="services-ticker">
  <!-- Background animated elements -->
  <div class="ticker-bg-animation">
    <div class="ticker-floating-orb ticker-orb-1"></div>
    <div class="ticker-floating-orb ticker-orb-2"></div>
    <div class="ticker-floating-orb ticker-orb-3"></div>
  </div>

  <!-- Main container -->
  <div class="premium-container">
    <!-- Premium Header -->
    <div class="premium-header">
      <h2 class="premium-title">Exceptional Services</h2>
      <p class="premium-subtitle">Crafted with precision, delivered with excellence</p>
    </div>

    <!-- Premium Services Grid -->
    <div class="premium-grid">
      {% for service in services %}
      <div class="premium-card" data-category="{{ service.category }}">
        <div class="premium-card-inner">
          <div class="premium-icon">
            <img src="{% static 'images/buttonlogo.png' %}" alt="{{ service.title }}" />
          </div>
          <h3 class="premium-service-title">{{ service.title }}</h3>
          <div class="premium-hover-line"></div>

          <!-- Service Description Tooltip -->
          {% if service.description %}
          <div class="service-tooltip">
            <div class="tooltip-content">
              <h4 class="tooltip-title">{{ service.title }}</h4>
              <p class="tooltip-description">{{ service.description }}</p>
              <div class="tooltip-arrow"></div>
            </div>
          </div>
          {% endif %}
        </div>
      </div>
      {% empty %}
      <div class="premium-card" data-category="Development">
        <div class="premium-card-inner">
          <div class="premium-icon">
            <img src="{% static 'images/buttonlogo.png' %}" alt="Web Development" />
          </div>
          <h3 class="premium-service-title">Web Development</h3>
          <p class="premium-service-desc">Modern & Responsive</p>
          <div class="premium-hover-line"></div>

          <!-- Service Description Tooltip -->
          <div class="service-tooltip">
            <div class="tooltip-content">
              <h4 class="tooltip-title">Web Development</h4>
              <p class="tooltip-description">We create stunning, responsive websites that work flawlessly across all devices. From custom web applications to e-commerce platforms, our development team brings your digital vision to life with cutting-edge technologies and best practices.</p>
              <div class="tooltip-arrow"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="premium-card" data-category="Design">
        <div class="premium-card-inner">
          <div class="premium-icon">
            <img src="{% static 'images/buttonlogo.png' %}" alt="Mobile UI/UX" />
          </div>
          <h3 class="premium-service-title">Mobile UI/UX</h3>
          <p class="premium-service-desc">User-Centered Design</p>
          <div class="premium-hover-line"></div>

          <!-- Service Description Tooltip -->
          <div class="service-tooltip">
            <div class="tooltip-content">
              <h4 class="tooltip-title">Mobile UI/UX</h4>
              <p class="tooltip-description">Our design team crafts intuitive and engaging mobile experiences that delight users and drive conversions. We focus on user research, wireframing, prototyping, and testing to ensure your app provides exceptional usability and visual appeal.</p>
              <div class="tooltip-arrow"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="premium-card" data-category="AI">
        <div class="premium-card-inner">
          <div class="premium-icon">
            <img src="{% static 'images/buttonlogo.png' %}" alt="AI Automation" />
          </div>
          <h3 class="premium-service-title">AI Automation</h3>
          <p class="premium-service-desc">Smart Solutions</p>
          <div class="premium-hover-line"></div>

          <!-- Service Description Tooltip -->
          <div class="service-tooltip">
            <div class="tooltip-content">
              <h4 class="tooltip-title">AI Automation</h4>
              <p class="tooltip-description">Transform your business processes with intelligent automation solutions. We implement AI-powered tools and workflows that streamline operations, reduce manual tasks, and provide valuable insights to help your business scale efficiently.</p>
              <div class="tooltip-arrow"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="premium-card" data-category="Branding">
        <div class="premium-card-inner">
          <div class="premium-icon">
            <img src="{% static 'images/buttonlogo.png' %}" alt="Brand Identity" />
          </div>
          <h3 class="premium-service-title">Brand Identity</h3>
          <p class="premium-service-desc">Visual Storytelling</p>
          <div class="premium-hover-line"></div>

          <!-- Service Description Tooltip -->
          <div class="service-tooltip">
            <div class="tooltip-content">
              <h4 class="tooltip-title">Brand Identity</h4>
              <p class="tooltip-description">Build a powerful brand that resonates with your audience. We create comprehensive brand identities including logos, color palettes, typography, and brand guidelines that tell your unique story and establish a strong market presence.</p>
              <div class="tooltip-arrow"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="premium-card" data-category="Marketing">
        <div class="premium-card-inner">
          <div class="premium-icon">
            <img src="{% static 'images/buttonlogo.png' %}" alt="Digital Marketing" />
          </div>
          <h3 class="premium-service-title">Digital Marketing</h3>
          <p class="premium-service-desc">Growth Strategy</p>
          <div class="premium-hover-line"></div>

          <!-- Service Description Tooltip -->
          <div class="service-tooltip">
            <div class="tooltip-content">
              <h4 class="tooltip-title">Digital Marketing</h4>
              <p class="tooltip-description">Drive growth with data-driven marketing strategies. Our comprehensive approach includes SEO, social media marketing, PPC advertising, content marketing, and analytics to maximize your online presence and ROI.</p>
              <div class="tooltip-arrow"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="premium-card" data-category="Video">
        <div class="premium-card-inner">
          <div class="premium-icon">
            <img src="{% static 'images/buttonlogo.png' %}" alt="Video Production" />
          </div>
          <h3 class="premium-service-title">Video Production</h3>
          <p class="premium-service-desc">Cinematic Quality</p>
          <div class="premium-hover-line"></div>

          <!-- Service Description Tooltip -->
          <div class="service-tooltip">
            <div class="tooltip-content">
              <h4 class="tooltip-title">Video Production</h4>
              <p class="tooltip-description">Create compelling video content that captures attention and drives engagement. From promotional videos and commercials to social media content and corporate presentations, we deliver professional video production services.</p>
              <div class="tooltip-arrow"></div>
            </div>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- Premium CTA -->
    <div class="premium-cta">
      <a href="{% url 'netfyre_app:contact' %}" class="premium-btn">
        <span>Start Your Project</span>
        <div class="premium-btn-arrow">→</div>
      </a>
    </div>
  </div>
</section>

<!-- Projects Section -->
<section id="projects">
  <div class="ticker-container">
    <!-- Black blur fade overlays -->
    <div class="projects-fade-left"></div>
    <div class="projects-fade-right"></div>

    <div class="overlay">
      <h1>This Is the Part Where You Say “Damn.”</h1>
      <a href="{% url 'netfyre_app:casestudies_list' %}" class="fancy-button">
        <span class="text">View Projects</span>
        <span class="arrow">→</span>
      </a>
    </div>

    <!-- Row 1 -->
    <div class="ticker-row">
      <div class="ticker-track-container">
        <div class="ticker-track ticker-left">
          {% for project in projects %} {% if project.image %}
          <img src="{{ project.image.url }}" alt="{{ project.title }}" />
          {% endif %} {% empty %}
          <img
            src="https://images.pexels.com/photos/3777943/pexels-photo-3777943.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 1"
          />
          <img
            src="https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 2"
          />
          <img
            src="https://images.pexels.com/photos/3225528/pexels-photo-3225528.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 3"
          />
          <img
            src="https://images.pexels.com/photos/2887718/pexels-photo-2887718.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 4"
          />
          <img
            src="https://images.pexels.com/photos/3225517/pexels-photo-3225517.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 5"
          />
          <img
            src="https://images.pexels.com/photos/1576937/pexels-photo-1576937.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 6"
          />
          <img
            src="https://images.pexels.com/photos/3777943/pexels-photo-3777943.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 7"
          />
          <img
            src="https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 8"
          />
          {% endfor %}
        </div>
      </div>
    </div>

    <!-- Row 2 (reverse direction) -->
    <div class="ticker-row">
      <div class="ticker-track-container">
        <div class="ticker-track ticker-right">
          {% for project in projects %} {% if project.image %}
          <img src="{{ project.image.url }}" alt="{{ project.title }}" />
          {% endif %} {% empty %}
          <img
            src="https://images.pexels.com/photos/3225528/pexels-photo-3225528.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 9"
          />
          <img
            src="https://images.pexels.com/photos/2887718/pexels-photo-2887718.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 10"
          />
          <img
            src="https://images.pexels.com/photos/3777943/pexels-photo-3777943.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 11"
          />
          <img
            src="https://images.pexels.com/photos/3225517/pexels-photo-3225517.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 12"
          />
          <img
            src="https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 13"
          />
          <img
            src="https://images.pexels.com/photos/1576937/pexels-photo-1576937.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 14"
          />
          <img
            src="https://images.pexels.com/photos/2887718/pexels-photo-2887718.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 15"
          />
          <img
            src="https://images.pexels.com/photos/3225528/pexels-photo-3225528.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 16"
          />
          {% endfor %}
        </div>
      </div>
    </div>

    <!-- Row 3 -->
    <div class="ticker-row">
      <div class="ticker-track-container">
        <div class="ticker-track ticker-left">
          {% for project in projects %} {% if project.image %}
          <img src="{{ project.image.url }}" alt="{{ project.title }}" />
          {% endif %} {% empty %}
          <img
            src="https://images.pexels.com/photos/3225528/pexels-photo-3225528.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 17"
          />
          <img
            src="https://images.pexels.com/photos/1576937/pexels-photo-1576937.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 18"
          />
          <img
            src="https://images.pexels.com/photos/3777943/pexels-photo-3777943.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 19"
          />
          <img
            src="https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 20"
          />
          <img
            src="https://images.pexels.com/photos/2887718/pexels-photo-2887718.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 21"
          />
          <img
            src="https://images.pexels.com/photos/3225528/pexels-photo-3225528.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 22"
          />
          <img
            src="https://images.pexels.com/photos/3225517/pexels-photo-3225517.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 23"
          />
          <img
            src="https://images.pexels.com/photos/2887718/pexels-photo-2887718.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            alt="Project 24"
          />
          {% endfor %}
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Testimonial Section -->
<section id="testimonial" class="py-20 px-4 w-full">
  <div class="w-full mx-auto">
    <!-- Header -->
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
        Here, Trust Earns Trust
      </h2>
      <h6 class="text-3xl md:text-4xl font-medium text-gray-700">
        Our client's successful stories
      </h6>
    </div>

    <!-- Testimonials Carousel -->
    <div class="testimonial-section">
      <!-- Fade overlays -->
      <div class="fade-left"></div>
      <div class="fade-right"></div>

      <!-- Scrolling track with duplicated content for seamless loop -->
      <div class="testimonial-track">
        <!-- First set of testimonials -->
        {% for testimonial in testimonials %}
        <div class="testimonial-box">
          <div class="flex items-start mb-4">
            {% if testimonial.client_image %}
            <img
              src="{{ testimonial.client_image.url }}"
              alt="{{ testimonial.client_name }}"
              class="profile-img"
            />
            {% else %}
            <img
              src="https://framerusercontent.com/images/qjXSHTqRYcf656b0syfedpKI.png"
              alt="{{ testimonial.client_name }}"
              class="profile-img"
            />
            {% endif %}
            <div>
              <div class="name">{{ testimonial.client_name }}</div>
              <div class="title">
                {{ testimonial.client_title }}, {{ testimonial.client_company }}
              </div>
            </div>
          </div>
          <p class="quote">{{ testimonial.testimonial_text }}</p>
        </div>
        {% empty %}
        <!-- Default testimonials if none exist -->
        <div class="testimonial-box">
          <div class="flex items-start mb-4">
            <img
              src="https://framerusercontent.com/images/qjXSHTqRYcf656b0syfedpKI.png"
              alt="Emily Thompson"
              class="profile-img"
            />
            <div>
              <div class="name">Emily Thompson</div>
              <div class="title">Marketing Director, Tech Innovators Inc.</div>
            </div>
          </div>
          <p class="quote">
            Working with NetFyre was a game-changer for us. Their expertise in
            UX/UI design transformed our user experience, resulting in a 30%
            increase in user engagement. We couldn't be happier!
          </p>
        </div>

        <div class="testimonial-box">
          <div class="flex items-start mb-4">
            <img
              src="https://framerusercontent.com/images/X9dPtQouHZeYSlEzRC1EJWcO4.jpg"
              alt="John Smith"
              class="profile-img"
            />
            <div>
              <div class="name">John Smith</div>
              <div class="title">CEO, Digital Solutions Co.</div>
            </div>
          </div>
          <p class="quote">
            NetFyre's digital marketing strategies helped us scale our business
            to new heights. Their data-driven approach and creative campaigns
            delivered exceptional ROI.
          </p>
        </div>

        <div class="testimonial-box">
          <div class="flex items-start mb-4">
            <img
              src="https://framerusercontent.com/images/CON0RXICvp6IYfKeETCf4sFLA.jpg"
              alt="Sarah Johnson"
              class="profile-img"
            />
            <div>
              <div class="name">Sarah Johnson</div>
              <div class="title">Founder, E-commerce Startup</div>
            </div>
          </div>
          <p class="quote">
            The team at NetFyre understood our vision perfectly. They created a
            brand identity that truly represents our values and resonates with
            our target audience.
          </p>
        </div>

        <div class="testimonial-box">
          <div class="flex items-start mb-4">
            <img
              src="https://framerusercontent.com/images/Rc7nZU7BNuq1WpB5gN8azS34.jpg"
              alt="Michael Brown"
              class="profile-img"
            />
            <div>
              <div class="name">Michael Brown</div>
              <div class="title">Product Manager, SaaS Company</div>
            </div>
          </div>
          <p class="quote">
            NetFyre's web development skills are top-notch. They delivered a
            fast, responsive, and user-friendly website that exceeded our
            expectations.
          </p>
        </div>

        <div class="testimonial-box">
          <div class="flex items-start mb-4">
            <img
              src="https://framerusercontent.com/images/4mRJd4C7YhebEQdXPIorVq6U.jpg"
              alt="Lisa Davis"
              class="profile-img"
            />
            <div>
              <div class="name">Lisa Davis</div>
              <div class="title">Creative Director, Media Agency</div>
            </div>
          </div>
          <p class="quote">
            The video production quality from NetFyre is outstanding. Their
            creative storytelling and technical expertise brought our brand
            story to life beautifully.
          </p>
        </div>
        {% endfor %}

        <!-- Duplicate set for seamless infinite scroll -->
        {% for testimonial in testimonials %}
        <div class="testimonial-box">
          <div class="flex items-start mb-4">
            {% if testimonial.client_image %}
            <img
              src="{{ testimonial.client_image.url }}"
              alt="{{ testimonial.client_name }}"
              class="profile-img"
            />
            {% else %}
            <img
              src="https://framerusercontent.com/images/qjXSHTqRYcf656b0syfedpKI.png"
              alt="{{ testimonial.client_name }}"
              class="profile-img"
            />
            {% endif %}
            <div>
              <div class="name">{{ testimonial.client_name }}</div>
              <div class="title">
                {{ testimonial.client_title }}, {{ testimonial.client_company }}
              </div>
            </div>
          </div>
          <p class="quote">{{ testimonial.testimonial_text }}</p>
        </div>
        {% empty %}
        <!-- Duplicate default testimonials for seamless scroll -->
        <div class="testimonial-box">
          <div class="flex items-start mb-4">
            <img
              src="https://framerusercontent.com/images/qjXSHTqRYcf656b0syfedpKI.png"
              alt="Emily Thompson"
              class="profile-img"
            />
            <div>
              <div class="name">Emily Thompson</div>
              <div class="title">Marketing Director, Tech Innovators Inc.</div>
            </div>
          </div>
          <p class="quote">
            Working with NetFyre was a game-changer for us. Their expertise in
            UX/UI design transformed our user experience, resulting in a 30%
            increase in user engagement. We couldn't be happier!
          </p>
        </div>

        <div class="testimonial-box">
          <div class="flex items-start mb-4">
            <img
              src="https://framerusercontent.com/images/X9dPtQouHZeYSlEzRC1EJWcO4.jpg"
              alt="John Smith"
              class="profile-img"
            />
            <div>
              <div class="name">John Smith</div>
              <div class="title">CEO, Digital Solutions Co.</div>
            </div>
          </div>
          <p class="quote">
            NetFyre's digital marketing strategies helped us scale our business
            to new heights. Their data-driven approach and creative campaigns
            delivered exceptional ROI.
          </p>
        </div>

        <div class="testimonial-box">
          <div class="flex items-start mb-4">
            <img
              src="https://framerusercontent.com/images/CON0RXICvp6IYfKeETCf4sFLA.jpg"
              alt="Sarah Johnson"
              class="profile-img"
            />
            <div>
              <div class="name">Sarah Johnson</div>
              <div class="title">Founder, E-commerce Startup</div>
            </div>
          </div>
          <p class="quote">
            The team at NetFyre understood our vision perfectly. They created a
            brand identity that truly represents our values and resonates with
            our target audience.
          </p>
        </div>

        <div class="testimonial-box">
          <div class="flex items-start mb-4">
            <img
              src="https://framerusercontent.com/images/Rc7nZU7BNuq1WpB5gN8azS34.jpg"
              alt="Michael Brown"
              class="profile-img"
            />
            <div>
              <div class="name">Michael Brown</div>
              <div class="title">Product Manager, SaaS Company</div>
            </div>
          </div>
          <p class="quote">
            NetFyre's web development skills are top-notch. They delivered a
            fast, responsive, and user-friendly website that exceeded our
            expectations.
          </p>
        </div>

        <div class="testimonial-box">
          <div class="flex items-start mb-4">
            <img
              src="https://framerusercontent.com/images/4mRJd4C7YhebEQdXPIorVq6U.jpg"
              alt="Lisa Davis"
              class="profile-img"
            />
            <div>
              <div class="name">Lisa Davis</div>
              <div class="title">Creative Director, Media Agency</div>
            </div>
          </div>
          <p class="quote">
            The video production quality from NetFyre is outstanding. Their
            creative storytelling and technical expertise brought our brand
            story to life beautifully.
          </p>
        </div>
        {% endfor %}
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section id="faq">
  <div class="faq-container">
    <div class="faq-left-section">
      <h1><span>(FAQ)</span><br />Things people<br />often ask</h1>
    </div>
    <div class="faq-right-section">
      {% for faq in faqs %}
      <div class="faq-item">
        <div class="faq-question">
          <h2>{{ faq.question }}</h2>
          <span class="faq-toggle"
            >{% if forloop.first %}−{% else %}+{% endif %}</span
          >
        </div>
        <div class="faq-answer {% if forloop.first %}show{% endif %}">
          {{ faq.answer }}
        </div>
      </div>
      {% empty %}
      <div class="faq-item">
        <div class="faq-question">
          <h2>How do we get started?</h2>
          <span class="faq-toggle">−</span>
        </div>
        <div class="faq-answer show">
          Super simple. Just hit the Let's Talk button or send me a quick email.
          Tell me a bit about your project, and I'll get back to you within 24
          hours to discuss the next steps.
        </div>
      </div>
      <div class="faq-item">
        <div class="faq-question">
          <h2>How long does a project take?</h2>
          <span class="faq-toggle">+</span>
        </div>
        <div class="faq-answer">
          One-pagers usually take 1–2 weeks. Full websites (3+ pages) usually
          take 3–4 weeks. I'll give you a clear timeline after we chat about
          your project needs.
        </div>
      </div>
      <div class="faq-item">
        <div class="faq-question">
          <h2>Do you work with agencies or white-label?</h2>
          <span class="faq-toggle">+</span>
        </div>
        <div class="faq-answer">
          Yep, I do! I'm open to collaborating silently as part of your team. I
          can jump in as your design partner or handle specific parts of the
          project under NDA or white-label terms.
        </div>
      </div>
      <div class="faq-item">
        <div class="faq-question">
          <h2>Can I pay in milestones?</h2>
          <span class="faq-toggle">+</span>
        </div>
        <div class="faq-answer">
          For sure! I usually do 50% upfront and 50% before final delivery, but
          I'm happy to adjust based on what works best for both of us. Just let
          me know your situation.
        </div>
      </div>
      <div class="faq-item">
        <div class="faq-question">
          <h2>Can you just design it and not build it?</h2>
          <span class="faq-toggle">+</span>
        </div>
        <div class="faq-answer">
          Absolutely! If you already have a dev team or just need the Figma
          design, I can deliver clean, organized design files ready for handoff
          with clear notes for your developers.
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</section>

<!-- CTA Section -->
<div class="cta-section">
  <div class="cta-container">
    <div class="cta-content">
      <h1 class="cta-heading">Let's Make It<br />Happen.</h1>
    </div>
    <button class="cta-button">
      <!-- Logo Icon -->
      <img
        src="{% static 'images/buttonlogo.png' %}"
        alt="Logo"
        class="cta-icon"
        width="30"
        height="30"
      />

      <!-- YOU Badge -->
      <div class="cta-badge">YOU</div>

      <!-- Button Text -->
      <span class="cta-text default-text">BOOK A CALL</span>
      <span class="cta-text hover-text">LET'S TALK!</span>
    </button>
  </div>

</div>

<script>
function handleCTAClick() {
  // Redirect to contact page
  window.location.href = "{% url 'netfyre_app:contact' %}";
}

// Initialize testimonial ticker and other functionality
document.addEventListener('DOMContentLoaded', function() {
  // Handle CTA buttons
  const ctaButtons = document.querySelectorAll('.cta-button');
  ctaButtons.forEach(button => {
    button.addEventListener('click', function() {
      window.location.href = "{% url 'netfyre_app:contact' %}";
    });
  });

  // Initialize testimonial ticker
  initTestimonialTicker();
});

function initTestimonialTicker() {
  const testimonialTrack = document.querySelector('.testimonial-track');
  if (!testimonialTrack) return;

  // Ensure smooth animation by setting proper width
  const testimonialBoxes = testimonialTrack.querySelectorAll('.testimonial-box');
  if (testimonialBoxes.length === 0) return;

  // Calculate total width for proper animation
  let totalWidth = 0;
  testimonialBoxes.forEach(box => {
    totalWidth += box.offsetWidth + 24; // 24px gap
  });

  // Set the track width for proper animation
  testimonialTrack.style.width = totalWidth + 'px';

  // Add smooth scrolling behavior
  testimonialTrack.style.willChange = 'transform';

  // Initialize service tooltips
  initializeServiceTooltips();
}

// Service Tooltip Functionality
function initializeServiceTooltips() {
  const premiumCards = document.querySelectorAll('.premium-card');
  let activeTooltip = null;

  premiumCards.forEach(card => {
    const tooltip = card.querySelector('.service-tooltip');
    if (!tooltip) return;

    // Desktop hover events
    card.addEventListener('mouseenter', () => {
      if (window.innerWidth > 768) {
        hideAllTooltips();
        showTooltip(tooltip);
        activeTooltip = tooltip;
      }
    });

    card.addEventListener('mouseleave', () => {
      if (window.innerWidth > 768) {
        hideTooltip(tooltip);
        activeTooltip = null;
      }
    });

    // Mobile touch events
    card.addEventListener('touchstart', (e) => {
      if (window.innerWidth <= 768) {
        e.preventDefault();

        if (activeTooltip === tooltip) {
          hideTooltip(tooltip);
          activeTooltip = null;
        } else {
          hideAllTooltips();
          showTooltip(tooltip);
          activeTooltip = tooltip;
        }
      }
    });
  });

  // Hide tooltips when clicking outside
  document.addEventListener('click', (e) => {
    if (!e.target.closest('.premium-card') && activeTooltip) {
      hideAllTooltips();
      activeTooltip = null;
    }
  });

  // Hide tooltips on scroll
  window.addEventListener('scroll', () => {
    if (activeTooltip) {
      hideAllTooltips();
      activeTooltip = null;
    }
  });
}

function showTooltip(tooltip) {
  tooltip.style.opacity = '1';
  tooltip.style.visibility = 'visible';
  tooltip.style.transform = 'translate(-50%, -50%) translateY(-5px)';
}

function hideTooltip(tooltip) {
  tooltip.style.opacity = '0';
  tooltip.style.visibility = 'hidden';
  tooltip.style.transform = 'translate(-50%, -50%)';
}

function hideAllTooltips() {
  const allTooltips = document.querySelectorAll('.service-tooltip');
  allTooltips.forEach(tooltip => {
    hideTooltip(tooltip);
  });
}
</script>
{% endblock %}
