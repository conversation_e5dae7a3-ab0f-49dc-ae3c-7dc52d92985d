# Generated by Django 5.2.1 on 2025-06-20 09:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('netfyre_app', '0008_alter_casestudy_options_casestudy_client_name_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='TeamMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(help_text='Full name', max_length=100)),
                ('role', models.Char<PERSON>ield(choices=[('founder', 'Founder'), ('ceo', 'CEO'), ('cto', 'CTO'), ('designer', 'Designer'), ('developer', 'Developer'), ('marketer', 'Digital Marketer'), ('strategist', 'Strategist'), ('manager', 'Project Manager'), ('analyst', 'Data Analyst'), ('copywriter', 'Copywriter'), ('videographer', 'Videographer'), ('other', 'Other')], help_text='Role/Position', max_length=50)),
                ('custom_role', models.CharField(blank=True, help_text="Custom role if 'Other' is selected", max_length=100)),
                ('bio', models.TextField(help_text='Professional biography')),
                ('short_bio', models.CharField(help_text='Short bio for cards/previews', max_length=200)),
                ('profile_image', models.ImageField(help_text='Professional headshot', upload_to='team/')),
                ('cover_image', models.ImageField(blank=True, help_text='Cover/background image', null=True, upload_to='team/covers/')),
                ('email', models.EmailField(blank=True, help_text='Professional email', max_length=254)),
                ('phone', models.CharField(blank=True, help_text='Phone number', max_length=20)),
                ('linkedin_url', models.URLField(blank=True, help_text='LinkedIn profile')),
                ('twitter_url', models.URLField(blank=True, help_text='Twitter profile')),
                ('instagram_url', models.URLField(blank=True, help_text='Instagram profile')),
                ('behance_url', models.URLField(blank=True, help_text='Behance portfolio')),
                ('dribbble_url', models.URLField(blank=True, help_text='Dribbble portfolio')),
                ('github_url', models.URLField(blank=True, help_text='GitHub profile')),
                ('personal_website', models.URLField(blank=True, help_text='Personal website')),
                ('years_experience', models.PositiveIntegerField(default=0, help_text='Years of experience')),
                ('specialties', models.CharField(help_text='Comma-separated specialties/skills', max_length=300)),
                ('achievements', models.TextField(blank=True, help_text='Key achievements and awards')),
                ('education', models.CharField(blank=True, help_text='Educational background', max_length=200)),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('is_featured', models.BooleanField(default=False, help_text='Show on homepage')),
                ('is_active', models.BooleanField(default=True, help_text='Active team member')),
                ('show_contact', models.BooleanField(default=True, help_text='Show contact information')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Team Member',
                'verbose_name_plural': 'Team Members',
                'ordering': ['order', 'name'],
            },
        ),
    ]
