from django.urls import path
from . import views

app_name = 'netfyre_app'

urlpatterns = [
    # Main pages
    path('', views.home, name='home'),
    path('about/', views.about, name='about'),
    path('team/', views.team, name='team'),
    path('team/<int:pk>/', views.team_member_detail, name='team_member_detail'),
    path('contact/', views.contact, name='contact'),
    path('privacy-policy/', views.privacy_policy, name='privacy_policy'),
    path('terms-and-conditions/', views.terms_conditions, name='terms_conditions'),
    path('api/cookie-consent/', views.cookie_consent, name='cookie_consent'),
    # Case Studies
    path('casestudies/', views.casestudies_list, name='casestudies_list'),
    path('casestudy/<slug:slug>/', views.casestudy_detail, name='casestudy_detail'),
    # Keep old URL for backward compatibility
    path('casestudy/', views.casestudy_redirect, name='casestudy'),
    
    # Services
    path('services/', views.services_list, name='services_list'),
    path('services/<int:pk>/', views.service_detail, name='service_detail'),
    
    # Projects
    path('projects/', views.projects_list, name='projects_list'),
    path('projects/<int:pk>/', views.project_detail, name='project_detail'),
    
    # Testimonials
    path('testimonials/', views.testimonials_list, name='testimonials_list'),
    
    # API endpoints
    path('api/services/', views.api_services, name='api_services'),
    path('api/testimonials/', views.api_testimonials, name='api_testimonials'),
    path('api/projects/', views.api_projects, name='api_projects'),
]
