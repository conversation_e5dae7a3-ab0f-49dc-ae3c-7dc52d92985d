{% extends 'base.html' %} {% load static %} {% block title %}{{ case_study.title
}} - {{ site_settings.site_title }}{% endblock %} {% block body_attributes %}
data-page="casestudy"{% endblock %} {% block extra_css %}
<link rel="stylesheet" href="{% static 'css/casestudy.css' %}?v=7" />
<link rel="stylesheet" href="{% static 'css/casestudy-navbar.css' %}?v=2" />
<style>
  /* Clean Case Study Page Styles */
  html,
  body {
    background: white !important;
    background-color: white !important;
    position: relative !important;
    overflow-x: hidden !important;
  }

  /* Case Study Header Styling */
  .casestudy-header {
    padding: 120px 20px 40px 20px !important;
    background: #ffffff !important;
    text-align: center !important;
  }

  .header-content {
    max-width: 800px !important;
    margin: 0 auto !important;
  }

  .casestudy-title {
    font-size: 3rem !important;
    font-weight: 700 !important;
    color: #1a1a1a !important;
    margin-bottom: 20px !important;
    line-height: 1.2 !important;
  }

  .casestudy-meta {
    display: flex !important;
    justify-content: center !important;
    gap: 30px !important;
    font-size: 16px !important;
    color: #666 !important;
  }

  .casestudy-meta .client,
  .casestudy-meta .category {
    font-weight: 500 !important;
    color: #ffb600 !important;
  }

  /* Hide home page elements for case study */
  .hero-gallery,
  .hero-section,
  .hero-bg-text,
  .bg-text-row {
    display: none !important;
  }

  /* Case study page layout */
  .case-study-page {
    position: relative !important;
    z-index: 10 !important;
    width: 100% !important;
    min-height: 100vh !important;
    background: white !important;
    display: block !important;
  }

  /* Reduce spacing between sections */
  .top-images {
    padding: 20px 0 !important;
    margin: 0 !important;
    background: transparent !important;
    position: relative;
    overflow: hidden;
  }

  .text-content {
    padding: 30px 20px !important;
    margin: 0 !important;
  }

  .hover-image-section {
    padding: 20px 0 !important;
    margin: 0 !important;
  }

  .video-section {
    padding: 20px 0 !important;
    margin: 0 !important;
  }

  .infinite-images {
    padding: 30px 0 !important;
    margin: 0 !important;
  }

  .related-casestudies {
    padding: 30px 0 !important;
    margin: 0 !important;
  }

  .casestudy-navigation {
    padding: 20px 0 40px 0 !important;
    margin: 0 !important;
  }

  /* Video wrapper styling */
  .video-wrapper {
    position: relative;
    width: 100%;
    max-width: 1000px;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    margin: 0 auto;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    background: #000;
  }

  /* Ensure iframe (YouTube) videos get proper styling */
  .video-wrapper iframe.center-video {
    border-radius: 15px !important;
    border: none !important;
  }

  /* Special handling for YouTube Shorts (vertical videos) */
  .video-wrapper.shorts {
    max-width: 400px;
    padding-bottom: 177.78%; /* 9:16 aspect ratio for vertical videos */
  }

  /* Detect if URL contains 'shorts' and apply different styling */
  .video-section[data-video-type="shorts"] .video-wrapper {
    max-width: 400px;
    padding-bottom: 177.78%; /* 9:16 aspect ratio */
  }

  /* Image overlay styles */
  .image-overlay-container {
    position: relative;
    display: inline-block;
    margin: 0 15px 15px 0;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transition: transform 0.3s cubic-bezier(0.4, 2, 0.6, 1);
  }

  /* Curved images container */
  .curved-images {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .image-overlay-container:hover {
    transform: scale(1.04) rotate(-1deg);
    z-index: 2;
  }

  .curved-square {
    display: block;
    width: 340px;
    height: 340px;
    object-fit: cover;
    border-radius: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transition: filter 0.3s cubic-bezier(0.4, 2, 0.6, 1);
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.18);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.35s cubic-bezier(0.4, 2, 0.6, 1);
  }

  .image-overlay-container:hover .image-overlay {
    opacity: 1;
    pointer-events: auto;
  }

  .overlay-logo {
    width: 400px;
    height: 400px;
    filter: drop-shadow(0 4px 16px rgba(0, 0, 0, 0.18));
    transition: opacity 0.5s cubic-bezier(0.4, 2, 0.6, 1),
      transform 0.5s cubic-bezier(0.4, 2, 0.6, 1);
    opacity: 0;
    transform: scale(0.7);
  }

  .image-overlay-container:hover .overlay-logo {
    opacity: 0.3;
    transform: scale(1);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .casestudy-header {
      padding: 100px 15px 30px 15px !important;
    }

    .casestudy-title {
      font-size: 2.2rem !important;
    }

    .casestudy-meta {
      flex-direction: column !important;
      gap: 10px !important;
    }

    .curved-images {
      flex-direction: column;
      gap: 15px;
      padding: 0 15px;
    }

    .curved-square {
      width: 90vw;
      height: 45vw;
      max-width: 340px;
      max-height: 340px;
    }

    .image-overlay-container {
      margin: 0;
    }

    .text-content {
      padding: 20px 15px !important;
    }

    .top-images,
    .hover-image-section,
    .video-section {
      padding: 15px 0 !important;
    }

    .infinite-images,
    .related-casestudies {
      padding: 20px 0 !important;
    }
  }
</style>
{% endblock %} {% block extra_js %}
<script>
  // Case Study Page JavaScript
  document.addEventListener("DOMContentLoaded", function () {
    // Clean up any home page elements
    const homeElements = document.querySelectorAll(
      ".hero-section, .hero-gallery, .hero-bg-text, .bg-text-row"
    );

    homeElements.forEach((element) => {
      if (element && !element.closest(".case-study-page")) {
        element.style.display = "none";
      }
    });

    // Set dynamic overlay logo for second curved square image
    const secondCurvedSquare = document.querySelector(
      ".curved-square:last-child[data-overlay-logo]"
    );
    console.log("Second curved square found:", secondCurvedSquare);

    if (secondCurvedSquare) {
      const overlayLogoUrl =
        secondCurvedSquare.getAttribute("data-overlay-logo");
      console.log("Overlay logo URL:", overlayLogoUrl);

      if (overlayLogoUrl) {
        // Create a style element to override the CSS
        const style = document.createElement("style");
        style.textContent = `
          .curved-square:last-child::after {
            background-image: url("${overlayLogoUrl}") !important;
            background-size: 120px 120px !important;
            background-repeat: no-repeat !important;
            background-position: center !important;
            opacity: 0 !important;
            transition: opacity 0.4s ease, transform 0.4s ease !important;
            z-index: 1 !important;
          }
          .curved-square:last-child:hover::after {
            opacity: 0.7 !important;
            transform: scale(1.05) !important;
          }
        `;
        document.head.appendChild(style);
        console.log("Dynamic overlay logo style applied!");
      }
    }

    // Set active nav link for case studies
    const caseStudyNavLink = document.querySelector('a[href*="casestudies"]');
    if (caseStudyNavLink) {
      caseStudyNavLink.classList.add("active");
    }

    // Initialize mobile menu for case study navbar
    initializeCaseStudyMobileMenu();
  });

  function initializeCaseStudyMobileMenu() {
    const mobileToggle = document.getElementById("mobileMenuToggle");
    const mobileMenu = document.getElementById("mobileNavMenu");

    if (mobileToggle && mobileMenu) {
      mobileToggle.addEventListener("click", function () {
        mobileMenu.classList.toggle("active");
      });

      // Close mobile menu when clicking outside
      document.addEventListener("click", function (e) {
        if (
          !mobileToggle.contains(e.target) &&
          !mobileMenu.contains(e.target)
        ) {
          mobileMenu.classList.remove("active");
        }
      });
    }
  }
</script>
{% endblock %} {% block content %}
<div class="case-study-page">
  <!-- Case Study Header -->
  <section class="casestudy-header">
    <div class="header-content">
      <h1 class="casestudy-title">{{ case_study.title }}</h1>
      <div class="casestudy-meta">
        <span class="client">{{ case_study.client_name }}</span>
        <span class="category">{{ case_study.project_category }}</span>
      </div>
    </div>
  </section>
  <!-- Top section with two curved square images -->
  <section class="top-images">
    <div class="curved-images">
      {% if case_study.image_1 %}
      <div class="image-overlay-container">
        <img
          src="{{ case_study.image_1.url }}"
          alt="{{ case_study.image_1_alt }}"
          class="curved-square"
        />
        <div class="image-overlay">
          <img
            src="{% static 'images/buttonlogo.png' %}"
            alt="Overlay Logo"
            class="overlay-logo"
          />
        </div>
      </div>
      {% else %}
      <div class="image-overlay-container">
        <img
          src="{% static 'images/img1.jpeg' %}"
          alt="Case Study Image 1"
          class="curved-square"
        />
        <div class="image-overlay">
          <img
            src="{% static 'images/buttonlogo.png' %}"
            alt="Overlay Logo"
            class="overlay-logo"
          />
        </div>
      </div>
      {% endif %} {% if case_study.image_2 %}
      <div class="image-overlay-container">
        <img
          src="{{ case_study.image_2.url }}"
          alt="{{ case_study.image_2_alt }}"
          class="curved-square"
        />
        <div class="image-overlay">
          <img
            src="{% static 'images/buttonlogo.png' %}"
            alt="Overlay Logo"
            class="overlay-logo"
          />
        </div>
      </div>
      {% else %}
      <div class="image-overlay-container">
        <img
          src="{% static 'images/img2.jpeg' %}"
          alt="Case Study Image 2"
          class="curved-square"
        />
        <div class="image-overlay">
          <img
            src="{% static 'images/buttonlogo.png' %}"
            alt="Overlay Logo"
            class="overlay-logo"
          />
        </div>
      </div>
      {% endif %}
    </div>
  </section>

  <!-- Black paragraph text in center -->
  <section class="text-content">
    <div class="center-text">
      {% if case_study.description %} {{ case_study.description|linebreaks }}
      <p>
        Case Study: Kemono - Built from Scratch. Scaled with Precision. When
        Kemono set out to enter the competitive world of weightlifting gear,
        they had nothing but an idea — no products, no branding, no website, no
        audience. That's when NetFyre stepped in. Over the course of a few
        months, we didn't just market Kemono — we built it.
      </p>

      <p>
        🌸 Brand Creation & Identity We collaborated closely with the Kemono
        team to develop the entire brand identity from the ground up — including
        the logo, packaging, visual tone, storytelling, and product positioning.
        Every design and word was crafted to give Kemono a bold and distinct
        personality in the lifting community.
      </p>

      <p>
        🌸 Product Design & Culture-Driven Concepts Our creative direction
        played a key role in the design of Kemono's signature products,
        including their unique pop culture-inspired belts like the Goku,
        Deadpool and Berserk editions. These weren't just lifting accessories —
        they were collectible, hype-driven pieces that sparked conversation and
        demand.
      </p>

      <p>
        📱 Organic Growth & Content Strategy We launched and scaled Kemono's
        social presence from zero, executing: Full-scale photoshoots & videos
        for launches High-performing reels reaching tens of thousands
        organically Consistent content designed to educate, entertain, and
        convert Community-building initiatives that fostered brand loyalty
      </p>
      {% endif %}
    </div>
  </section>

  <!-- Center image with hover animation -->
  <section class="hover-image-section">
    {% if case_study.hover_image %}
    <img
      src="{{ case_study.hover_image.url }}"
      alt="{{ case_study.hover_image_alt }}"
      class="hover-image"
    />
    {% else %}
    <img
      src="{% static 'images/img5.jpeg' %}"
      alt="Hover Image"
      class="hover-image"
    />
    {% endif %}
  </section>

  <!-- Video section -->
  <section class="video-section" {% if case_study.video_url and 'shorts' in case_study.video_url %}data-video-type="shorts"{% endif %}>
    <div class="video-wrapper{% if case_study.video_url and 'shorts' in case_study.video_url %} shorts{% endif %}">
      {% if case_study.video_file %}
      <!-- Use uploaded video file -->
      <video
        {%
        if
        case_study.video_controls
        %}controls{%
        endif
        %}
        {%
        if
        case_study.video_autoplay
        %}autoplay{%
        endif
        %}
        {%
        if
        case_study.video_muted
        %}muted{%
        endif
        %}
        {%
        if
        case_study.video_loop
        %}loop{%
        endif
        %}
        playsinline
        class="center-video"
        {%
        if
        case_study.video_poster
        %}poster="{{ case_study.video_poster.url }}"
        {%
        endif
        %}
      >
        <source src="{{ case_study.video_file.url }}" type="video/mp4" />
        Your browser does not support the video tag.
      </video>
      {% elif case_study.video_url %}
      <!-- Handle YouTube and other video URLs -->
      {% if case_study.is_youtube_url %}
      <!-- YouTube embed -->
      <iframe
        width="100%"
        height="100%"
        src="{{ case_study.get_youtube_embed_url }}{% if case_study.video_autoplay %}?autoplay=1{% endif %}{% if case_study.video_muted %}&mute=1{% endif %}{% if case_study.video_loop %}&loop=1{% endif %}"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowfullscreen
        class="center-video"
      ></iframe>
      {% else %}
      <!-- Direct video URL -->
      <video
        {%
        if
        case_study.video_controls
        %}controls{%
        endif
        %}
        {%
        if
        case_study.video_autoplay
        %}autoplay{%
        endif
        %}
        {%
        if
        case_study.video_muted
        %}muted{%
        endif
        %}
        {%
        if
        case_study.video_loop
        %}loop{%
        endif
        %}
        playsinline
        class="center-video"
        {%
        if
        case_study.video_poster
        %}poster="{{ case_study.video_poster.url }}"
        {%
        endif
        %}
      >
        <source src="{{ case_study.video_url }}" type="video/mp4" />
        Your browser does not support the video tag.
      </video>
      {% endif %} {% else %}
      <!-- Fallback video -->
      <video
        {%
        if
        case_study.video_controls
        %}controls{%
        endif
        %}
        {%
        if
        case_study.video_autoplay
        %}autoplay{%
        endif
        %}
        {%
        if
        case_study.video_muted
        %}muted{%
        endif
        %}
        {%
        if
        case_study.video_loop
        %}loop{%
        endif
        %}
        playsinline
        class="center-video"
      >
        <source
          src="https://www.w3schools.com/html/mov_bbb.mp4"
          type="video/mp4"
        />
        Your browser does not support the video tag.
      </video>
      {% endif %}
    </div>
  </section>

  <!-- Infinite moving images (right to left) - Framer Style -->
  <section class="infinite-images">
    <h3>Works For Kemono</h3>
    <div class="images-right-to-left">
      <!-- Repeat images multiple times for seamless scroll -->
      {% if infinite_scroll_images %} {% for i in "123456789012345678901234" %}
      {% for image in infinite_scroll_images %}
      <img
        src="{{ image.image.url }}"
        alt="{{ image.alt_text|default:image.title }}"
        title="{{ image.title }}"
      />
      {% endfor %} {% endfor %} {% else %}
      <!-- Fallback to static images if no admin images are available -->
      {% for i in "123456789012345678901234" %}
      <img src="{% static 'images/img1.jpeg' %}" alt="Portfolio Image" />
      <img src="{% static 'images/img2.jpeg' %}" alt="Portfolio Image" />
      <img src="{% static 'images/img3.jpeg' %}" alt="Portfolio Image" />
      <img src="{% static 'images/img4.jpeg' %}" alt="Portfolio Image" />
      <img src="{% static 'images/img5.jpeg' %}" alt="Portfolio Image" />
      <img src="{% static 'images/img6.jpeg' %}" alt="Portfolio Image" />
      {% endfor %} {% endif %}
    </div>
  </section>

  <!-- Related Case Studies -->
  {% if related_case_studies %}
  <section class="related-casestudies">
    <h3>More Case Studies</h3>
    <div class="related-grid">
      {% for related in related_case_studies %}
      <div class="related-card">
        {% if related.image_1 %}
        <img src="{{ related.image_1.url }}" alt="{{ related.title }}" />
        {% endif %}
        <h4>{{ related.title }}</h4>
        <p>{{ related.client_name }}</p>
        <a href="{{ related.get_absolute_url }}">View Case Study →</a>
      </div>
      {% endfor %}
    </div>
  </section>
  {% endif %}

  <!-- Navigation -->
  <section class="casestudy-navigation">
    <a href="{% url 'netfyre_app:casestudies_list' %}" class="back-btn"
      >← All Case Studies</a
    >
    <a href="{% url 'netfyre_app:contact' %}" class="contact-btn"
      >Start Your Project →</a
    >
  </section>
</div>
{% endblock %}
